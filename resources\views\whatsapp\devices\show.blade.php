<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $device->device_name }} - Device Details</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <meta name="csrf-token" content="{{ csrf_token() }}">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen">
        <!-- Navigation -->
        <nav class="bg-green-600 text-white p-4">
            <div class="container mx-auto flex justify-between items-center">
                <h1 class="text-2xl font-bold">
                    <i class="fab fa-whatsapp mr-2"></i>
                    Device Details: {{ $device->device_name }}
                </h1>
                <div class="space-x-4">
                    <a href="{{ route('whatsapp.dashboard') }}" class="hover:text-green-200">Dashboard</a>
                    <a href="{{ route('whatsapp.devices.index') }}" class="hover:text-green-200">Device Management</a>
                    <a href="{{ route('whatsapp.sessions') }}" class="hover:text-green-200">Sessions</a>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="container mx-auto p-6">
            <div class="max-w-6xl mx-auto">
                <!-- Header -->
                <div class="flex justify-between items-center mb-6">
                    <div>
                        <h2 class="text-3xl font-bold text-gray-800">{{ $device->device_name }}</h2>
                        <p class="text-gray-600">Device ID: <span class="font-mono">{{ $device->device_id }}</span></p>
                    </div>
                    <div class="flex space-x-4">
                        @if($device->status === 'pending' || $device->status === 'disconnected')
                            <a href="{{ route('whatsapp.devices.connect', $device->device_id) }}" 
                               class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700">
                                <i class="fas fa-plug mr-2"></i>Connect Device
                            </a>
                        @endif
                        
                        @if($device->status === 'connected' && $device->session_id)
                            <a href="{{ route('whatsapp.send.message', $device->session_id) }}" 
                               class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
                                <i class="fas fa-paper-plane mr-2"></i>Send Message
                            </a>
                        @endif
                    </div>
                </div>

                <!-- Device Information -->
                <div class="grid md:grid-cols-2 gap-6 mb-8">
                    <!-- Basic Info -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold mb-4">Device Information</h3>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Device Name:</span>
                                <span class="font-semibold">{{ $device->device_name }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Device ID:</span>
                                <span class="font-mono text-sm">{{ $device->device_id }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Connection Method:</span>
                                <span class="px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800">
                                    {{ $device->connection_method_display }}
                                </span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Status:</span>
                                <span class="px-2 py-1 rounded-full text-xs {{ $device->status_badge_class }}">
                                    {{ $device->status_display }}
                                </span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Created:</span>
                                <span>{{ $device->created_at->format('M d, Y H:i') }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Last Updated:</span>
                                <span>{{ $device->updated_at->format('M d, Y H:i') }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- WhatsApp Info -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold mb-4">WhatsApp Information</h3>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Phone Number:</span>
                                <span class="font-semibold">{{ $device->phone_number ?? 'Not connected' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Display Name:</span>
                                <span class="font-semibold">{{ $device->display_name ?? 'Not available' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Session ID:</span>
                                <span class="font-mono text-sm">{{ $device->session_id ?? 'No session' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Last Connected:</span>
                                <span>{{ $device->last_connected_at ? $device->last_connected_at->format('M d, Y H:i') : 'Never' }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sessions History -->
                <div class="bg-white rounded-lg shadow mb-6">
                    <div class="p-6 border-b">
                        <h3 class="text-lg font-semibold">Session History</h3>
                    </div>
                    <div class="p-6">
                        @if($device->sessions->count() > 0)
                            <div class="overflow-x-auto">
                                <table class="min-w-full table-auto">
                                    <thead>
                                        <tr class="bg-gray-50">
                                            <th class="px-4 py-2 text-left">Session ID</th>
                                            <th class="px-4 py-2 text-left">Status</th>
                                            <th class="px-4 py-2 text-left">Phone Number</th>
                                            <th class="px-4 py-2 text-left">Name</th>
                                            <th class="px-4 py-2 text-left">Created</th>
                                            <th class="px-4 py-2 text-left">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($device->sessions as $session)
                                            <tr class="border-b hover:bg-gray-50">
                                                <td class="px-4 py-2 font-mono text-sm">{{ $session->session_id }}</td>
                                                <td class="px-4 py-2">
                                                    <span class="px-2 py-1 rounded-full text-xs {{ $session->status_badge_class }}">
                                                        {{ $session->status_display }}
                                                    </span>
                                                </td>
                                                <td class="px-4 py-2">{{ $session->phone_number ?? 'N/A' }}</td>
                                                <td class="px-4 py-2">{{ $session->name ?? 'N/A' }}</td>
                                                <td class="px-4 py-2">{{ $session->created_at->format('M d, Y H:i') }}</td>
                                                <td class="px-4 py-2">
                                                    <div class="flex space-x-2">
                                                        <a href="{{ route('whatsapp.session.detail', $session->session_id) }}" 
                                                           class="text-blue-600 hover:text-blue-800" title="View Session">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        @if($session->status === 'connected')
                                                            <a href="{{ route('whatsapp.send.message', $session->session_id) }}" 
                                                               class="text-green-600 hover:text-green-800" title="Send Message">
                                                                <i class="fas fa-paper-plane"></i>
                                                            </a>
                                                        @endif
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="text-center py-8">
                                <i class="fas fa-history text-4xl text-gray-400 mb-4"></i>
                                <p class="text-gray-600">No sessions found for this device.</p>
                                @if($device->status === 'pending')
                                    <p class="text-sm text-gray-500 mt-2">Connect the device to create your first session.</p>
                                @endif
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Real-time Status -->
                <div class="bg-white rounded-lg shadow mb-6">
                    <div class="p-6 border-b">
                        <h3 class="text-lg font-semibold">Real-time Status</h3>
                    </div>
                    <div class="p-6">
                        <div id="connection-status" class="flex items-center mb-4">
                            <div class="w-3 h-3 bg-gray-500 rounded-full mr-3"></div>
                            <span class="text-gray-600">Checking connection status...</span>
                        </div>
                        
                        <!-- Status Messages -->
                        <div id="status-messages" class="space-y-2 max-h-32 overflow-y-auto">
                            <!-- Real-time status messages will appear here -->
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex justify-between items-center">
                    <a href="{{ route('whatsapp.devices.index') }}" class="text-gray-600 hover:text-gray-800">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Devices
                    </a>
                    
                    <div class="space-x-4">
                        @if($device->status !== 'connected')
                            <button onclick="deleteDevice()" class="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700">
                                <i class="fas fa-trash mr-2"></i>Delete Device
                            </button>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Configuration
        const deviceId = '{{ $device->device_id }}';
        const sessionId = '{{ $device->session_id }}';
        const socketUrl = '{{ config("whatsapp.socket_url", "http://localhost:3000") }}';
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        // Socket.IO connection for real-time updates
        const socket = io(socketUrl);
        
        socket.on('connect', () => {
            updateConnectionStatus('connected', 'Connected to WhatsApp Gateway');
            addStatusMessage('Connected to WhatsApp Gateway', 'success');
        });
        
        socket.on('disconnect', () => {
            updateConnectionStatus('disconnected', 'Disconnected from Gateway');
            addStatusMessage('Disconnected from Gateway', 'error');
        });
        
        socket.on('session-status', (data) => {
            if (data.sessionId === sessionId) {
                handleSessionStatus(data.status, data.info);
            }
        });
        
        // Handle session status updates
        function handleSessionStatus(status, info) {
            addStatusMessage(`Session status: ${status}`, 'info');
            
            if (status === 'connected') {
                updateConnectionStatus('connected', 'Device is connected and ready');
                
                // Update device status
                updateDeviceStatus('connected', info);
            } else if (status === 'disconnected') {
                updateConnectionStatus('disconnected', 'Device disconnected');
                updateDeviceStatus('disconnected');
            }
        }
        
        // Update connection status display
        function updateConnectionStatus(status, message) {
            const statusElement = document.getElementById('connection-status');
            const statusClasses = {
                'connected': 'text-green-600',
                'disconnected': 'text-red-600',
                'connecting': 'text-blue-600'
            };
            
            const dotClasses = {
                'connected': 'bg-green-500',
                'disconnected': 'bg-red-500',
                'connecting': 'bg-blue-500 animate-pulse'
            };
            
            statusElement.innerHTML = `
                <div class="w-3 h-3 ${dotClasses[status] || 'bg-gray-500'} rounded-full mr-3"></div>
                <span class="${statusClasses[status] || 'text-gray-600'}">${message}</span>
            `;
        }
        
        // Add status message
        function addStatusMessage(message, type) {
            const messagesContainer = document.getElementById('status-messages');
            const messageElement = document.createElement('div');
            
            const typeClasses = {
                'success': 'text-green-600',
                'error': 'text-red-600',
                'info': 'text-blue-600'
            };
            
            const typeIcons = {
                'success': 'fa-check-circle',
                'error': 'fa-exclamation-circle',
                'info': 'fa-info-circle'
            };
            
            messageElement.className = `text-sm ${typeClasses[type] || 'text-gray-600'}`;
            messageElement.innerHTML = `
                <i class="fas ${typeIcons[type] || 'fa-circle'} mr-2"></i>
                <span class="text-gray-500">${new Date().toLocaleTimeString()}</span> - ${message}
            `;
            
            messagesContainer.appendChild(messageElement);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        // Update device status via API
        async function updateDeviceStatus(status, info) {
            try {
                const updateData = { status };
                
                if (info) {
                    if (info.id) updateData.phone_number = info.id;
                    if (info.name) updateData.display_name = info.name;
                }
                
                await fetch(`/api/whatsapp/devices/${deviceId}/status`, {
                    method: 'PATCH',
                    headers: {
                        'X-CSRF-TOKEN': csrfToken,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(updateData)
                });
                
                // Reload page to show updated info
                setTimeout(() => {
                    location.reload();
                }, 2000);
                
            } catch (error) {
                console.error('Failed to update device status:', error);
            }
        }
        
        // Delete device
        async function deleteDevice() {
            if (!confirm('Are you sure you want to delete this device? This action cannot be undone and will disconnect any active sessions.')) {
                return;
            }
            
            try {
                const response = await fetch(`/api/whatsapp/devices/${deviceId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': csrfToken,
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    window.location.href = '{{ route("whatsapp.devices.index") }}';
                } else {
                    alert('Failed to delete device: ' + (result.message || 'Unknown error'));
                }
            } catch (error) {
                alert('Error deleting device: ' + error.message);
            }
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            addStatusMessage('Page loaded - monitoring device status', 'info');
        });
    </script>
</body>
</html>
