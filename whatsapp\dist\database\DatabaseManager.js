import mysql from 'mysql2/promise';
import dotenv from 'dotenv';
dotenv.config({ path: '../.env' });
export class DatabaseManager {
    connection = null;
    constructor() {
        this.initializeConnection();
    }
    async initializeConnection() {
        try {
            this.connection = await mysql.createConnection({
                host: process.env.DB_HOST || 'localhost',
                port: parseInt(process.env.DB_PORT || '3306'),
                user: process.env.DB_USERNAME || 'root',
                password: process.env.DB_PASSWORD || '',
                database: process.env.DB_DATABASE || 'wablasid',
                timezone: '+00:00'
            });
            console.log('Database connected successfully');
            await this.createTables();
        }
        catch (error) {
            console.error('Database connection error:', error);
            throw error;
        }
    }
    async createTables() {
        if (!this.connection)
            throw new Error('Database not connected');
        try {
            // Create devices table
            await this.connection.execute(`
        CREATE TABLE IF NOT EXISTS whatsapp_devices (
          id INT AUTO_INCREMENT PRIMARY KEY,
          device_name VARCHAR(255) NOT NULL,
          device_id VARCHAR(255) UNIQUE NOT NULL,
          connection_method ENUM('qr_code', 'auth_code') DEFAULT 'qr_code',
          status ENUM('pending', 'connecting', 'connected', 'disconnected', 'failed') DEFAULT 'pending',
          phone_number VARCHAR(20) NULL,
          display_name VARCHAR(255) NULL,
          session_id VARCHAR(255) NULL,
          qr_code TEXT NULL,
          auth_code VARCHAR(20) NULL,
          last_connected_at TIMESTAMP NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_device_id (device_id),
          INDEX idx_status (status),
          INDEX idx_session_id (session_id)
        )
      `);
            // Create sessions table
            await this.connection.execute(`
        CREATE TABLE IF NOT EXISTS whatsapp_sessions (
          id INT AUTO_INCREMENT PRIMARY KEY,
          session_id VARCHAR(255) UNIQUE NOT NULL,
          device_id INT NULL,
          status ENUM('connecting', 'connected', 'disconnected', 'qr') DEFAULT 'connecting',
          phone_number VARCHAR(20) NULL,
          name VARCHAR(255) NULL,
          qr_code TEXT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_session_id (session_id),
          INDEX idx_status (status),
          INDEX idx_device_id (device_id),
          FOREIGN KEY (device_id) REFERENCES whatsapp_devices(id) ON DELETE SET NULL
        )
      `);
            // Create messages table
            await this.connection.execute(`
        CREATE TABLE IF NOT EXISTS whatsapp_messages (
          id INT AUTO_INCREMENT PRIMARY KEY,
          message_id VARCHAR(255) NOT NULL,
          session_id VARCHAR(255) NOT NULL,
          from_jid VARCHAR(255) NOT NULL,
          from_me BOOLEAN DEFAULT FALSE,
          participant VARCHAR(255) NULL,
          message_type VARCHAR(50) NOT NULL,
          text_content TEXT NULL,
          media_url VARCHAR(500) NULL,
          media_type VARCHAR(50) NULL,
          media_size INT NULL,
          timestamp TIMESTAMP NOT NULL,
          status ENUM('sent', 'delivered', 'read', 'received', 'failed') DEFAULT 'sent',
          raw_message LONGTEXT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          UNIQUE KEY unique_message (message_id, session_id),
          INDEX idx_session_id (session_id),
          INDEX idx_from_jid (from_jid),
          INDEX idx_timestamp (timestamp),
          INDEX idx_message_type (message_type),
          FOREIGN KEY (session_id) REFERENCES whatsapp_sessions(session_id) ON DELETE CASCADE
        )
      `);
            // Create contacts table
            await this.connection.execute(`
        CREATE TABLE IF NOT EXISTS whatsapp_contacts (
          id INT AUTO_INCREMENT PRIMARY KEY,
          session_id VARCHAR(255) NOT NULL,
          jid VARCHAR(255) NOT NULL,
          phone_number VARCHAR(20) NULL,
          name VARCHAR(255) NULL,
          is_group BOOLEAN DEFAULT FALSE,
          last_seen TIMESTAMP NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          UNIQUE KEY unique_contact (session_id, jid),
          INDEX idx_session_id (session_id),
          INDEX idx_phone_number (phone_number),
          INDEX idx_is_group (is_group),
          FOREIGN KEY (session_id) REFERENCES whatsapp_sessions(session_id) ON DELETE CASCADE
        )
      `);
            // Create webhooks table
            await this.connection.execute(`
        CREATE TABLE IF NOT EXISTS whatsapp_webhooks (
          id INT AUTO_INCREMENT PRIMARY KEY,
          session_id VARCHAR(255) NOT NULL,
          url VARCHAR(500) NOT NULL,
          events JSON NOT NULL,
          is_active BOOLEAN DEFAULT TRUE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_session_id (session_id),
          INDEX idx_is_active (is_active),
          FOREIGN KEY (session_id) REFERENCES whatsapp_sessions(session_id) ON DELETE CASCADE
        )
      `);
            console.log('Database tables created successfully');
        }
        catch (error) {
            console.error('Error creating tables:', error);
            throw error;
        }
    }
    // Device management methods
    async storeDevice(deviceData) {
        if (!this.connection)
            throw new Error('Database not connected');
        try {
            const [result] = await this.connection.execute(`INSERT INTO whatsapp_devices (device_name, device_id, connection_method, status, phone_number, display_name, session_id, qr_code, auth_code, last_connected_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
         ON DUPLICATE KEY UPDATE
         device_name = VALUES(device_name),
         connection_method = VALUES(connection_method),
         status = VALUES(status),
         phone_number = VALUES(phone_number),
         display_name = VALUES(display_name),
         session_id = VALUES(session_id),
         qr_code = VALUES(qr_code),
         auth_code = VALUES(auth_code),
         last_connected_at = VALUES(last_connected_at),
         updated_at = CURRENT_TIMESTAMP`, [
                deviceData.device_name,
                deviceData.device_id,
                deviceData.connection_method,
                deviceData.status,
                deviceData.phone_number || null,
                deviceData.display_name || null,
                deviceData.session_id || null,
                deviceData.qr_code || null,
                deviceData.auth_code || null,
                deviceData.last_connected_at || null
            ]);
            return result.insertId;
        }
        catch (error) {
            console.error('Error storing device:', error);
            throw error;
        }
    }
    async updateDevice(deviceId, updates) {
        if (!this.connection)
            throw new Error('Database not connected');
        const fields = [];
        const values = [];
        for (const [key, value] of Object.entries(updates)) {
            if (key !== 'id' && key !== 'device_id') {
                fields.push(`${key} = ?`);
                values.push(value);
            }
        }
        if (fields.length === 0)
            return;
        fields.push('updated_at = CURRENT_TIMESTAMP');
        values.push(deviceId);
        try {
            await this.connection.execute(`UPDATE whatsapp_devices SET ${fields.join(', ')} WHERE device_id = ?`, values);
        }
        catch (error) {
            console.error('Error updating device:', error);
            throw error;
        }
    }
    async getDevice(deviceId) {
        if (!this.connection)
            throw new Error('Database not connected');
        try {
            const [rows] = await this.connection.execute('SELECT * FROM whatsapp_devices WHERE device_id = ?', [deviceId]);
            const devices = rows;
            return devices.length > 0 ? devices[0] : null;
        }
        catch (error) {
            console.error('Error getting device:', error);
            throw error;
        }
    }
    async getAllDevices() {
        if (!this.connection)
            throw new Error('Database not connected');
        try {
            const [rows] = await this.connection.execute('SELECT * FROM whatsapp_devices ORDER BY created_at DESC');
            return rows;
        }
        catch (error) {
            console.error('Error getting all devices:', error);
            throw error;
        }
    }
    async deleteDevice(deviceId) {
        if (!this.connection)
            throw new Error('Database not connected');
        try {
            await this.connection.execute('DELETE FROM whatsapp_devices WHERE device_id = ?', [deviceId]);
        }
        catch (error) {
            console.error('Error deleting device:', error);
            throw error;
        }
    }
    async storeSession(sessionData) {
        if (!this.connection)
            throw new Error('Database not connected');
        try {
            await this.connection.execute(`INSERT INTO whatsapp_sessions (session_id, device_id, status, phone_number, name, qr_code)
         VALUES (?, ?, ?, ?, ?, ?)
         ON DUPLICATE KEY UPDATE
         device_id = VALUES(device_id),
         status = VALUES(status),
         phone_number = VALUES(phone_number),
         name = VALUES(name),
         qr_code = VALUES(qr_code),
         updated_at = CURRENT_TIMESTAMP`, [
                sessionData.session_id,
                sessionData.device_id || null,
                sessionData.status,
                sessionData.phone_number || null,
                sessionData.name || null,
                sessionData.qr_code || null
            ]);
        }
        catch (error) {
            console.error('Error storing session:', error);
            throw error;
        }
    }
    async updateSession(sessionId, updates) {
        if (!this.connection)
            throw new Error('Database not connected');
        try {
            const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ');
            const values = Object.values(updates);
            values.push(sessionId);
            await this.connection.execute(`UPDATE whatsapp_sessions SET ${fields}, updated_at = CURRENT_TIMESTAMP WHERE session_id = ?`, values);
        }
        catch (error) {
            console.error('Error updating session:', error);
            throw error;
        }
    }
    async getSession(sessionId) {
        if (!this.connection)
            throw new Error('Database not connected');
        try {
            const [rows] = await this.connection.execute('SELECT * FROM whatsapp_sessions WHERE session_id = ?', [sessionId]);
            const sessions = rows;
            return sessions.length > 0 ? sessions[0] : null;
        }
        catch (error) {
            console.error('Error getting session:', error);
            throw error;
        }
    }
    async getAllSessions() {
        if (!this.connection)
            throw new Error('Database not connected');
        try {
            const [rows] = await this.connection.execute('SELECT * FROM whatsapp_sessions ORDER BY created_at DESC');
            return rows;
        }
        catch (error) {
            console.error('Error getting all sessions:', error);
            throw error;
        }
    }
    async storeMessage(messageData) {
        if (!this.connection)
            throw new Error('Database not connected');
        try {
            await this.connection.execute(`INSERT INTO whatsapp_messages 
         (message_id, session_id, from_jid, from_me, participant, message_type, 
          text_content, media_url, media_type, media_size, timestamp, status, raw_message) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
         ON DUPLICATE KEY UPDATE
         status = VALUES(status),
         raw_message = VALUES(raw_message)`, [
                messageData.message_id,
                messageData.session_id,
                messageData.from_jid,
                messageData.from_me,
                messageData.participant || null,
                messageData.message_type,
                messageData.text_content || null,
                messageData.media_url || null,
                messageData.media_type || null,
                messageData.media_size || null,
                messageData.timestamp,
                messageData.status,
                messageData.raw_message || null
            ]);
        }
        catch (error) {
            console.error('Error storing message:', error);
            throw error;
        }
    }
    async updateMessageStatus(messageId, status) {
        if (!this.connection)
            throw new Error('Database not connected');
        try {
            await this.connection.execute('UPDATE whatsapp_messages SET status = ? WHERE message_id = ?', [status, messageId]);
        }
        catch (error) {
            console.error('Error updating message status:', error);
            throw error;
        }
    }
    async getMessage(messageId) {
        if (!this.connection)
            throw new Error('Database not connected');
        try {
            const [rows] = await this.connection.execute('SELECT * FROM whatsapp_messages WHERE message_id = ?', [messageId]);
            const messages = rows;
            return messages.length > 0 ? messages[0] : null;
        }
        catch (error) {
            console.error('Error getting message:', error);
            throw error;
        }
    }
    async getMessages(sessionId, limit = 50, offset = 0) {
        if (!this.connection)
            throw new Error('Database not connected');
        try {
            const [rows] = await this.connection.execute(`SELECT * FROM whatsapp_messages 
         WHERE session_id = ? 
         ORDER BY timestamp DESC 
         LIMIT ? OFFSET ?`, [sessionId, limit, offset]);
            return rows;
        }
        catch (error) {
            console.error('Error getting messages:', error);
            throw error;
        }
    }
    async storeContact(contactData) {
        if (!this.connection)
            throw new Error('Database not connected');
        try {
            await this.connection.execute(`INSERT INTO whatsapp_contacts 
         (session_id, jid, phone_number, name, is_group, last_seen) 
         VALUES (?, ?, ?, ?, ?, ?)
         ON DUPLICATE KEY UPDATE
         phone_number = VALUES(phone_number),
         name = VALUES(name),
         last_seen = VALUES(last_seen),
         updated_at = CURRENT_TIMESTAMP`, [
                contactData.session_id,
                contactData.jid,
                contactData.phone_number || null,
                contactData.name || null,
                contactData.is_group,
                contactData.last_seen
            ]);
        }
        catch (error) {
            console.error('Error storing contact:', error);
            throw error;
        }
    }
    async getContacts(sessionId) {
        if (!this.connection)
            throw new Error('Database not connected');
        try {
            const [rows] = await this.connection.execute('SELECT * FROM whatsapp_contacts WHERE session_id = ? ORDER BY last_seen DESC', [sessionId]);
            return rows;
        }
        catch (error) {
            console.error('Error getting contacts:', error);
            throw error;
        }
    }
    async close() {
        if (this.connection) {
            await this.connection.end();
            this.connection = null;
        }
    }
}
//# sourceMappingURL=DatabaseManager.js.map