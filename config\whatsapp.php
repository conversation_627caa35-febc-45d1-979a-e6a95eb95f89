<?php

return [
    /*
    |--------------------------------------------------------------------------
    | WhatsApp Gateway Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration options for the WhatsApp Gateway
    | integration using @WhiskeySockets/Baileys library.
    |
    */

    'gateway_url' => env('WHATSAPP_GATEWAY_URL', 'http://localhost:3000'),
    
    'gateway_port' => env('WHATSAPP_GATEWAY_PORT', 3000),
    
    'socket_url' => env('WHATSAPP_SOCKET_URL', 'http://localhost:3000'),
    
    /*
    |--------------------------------------------------------------------------
    | Session Configuration
    |--------------------------------------------------------------------------
    */
    
    'session' => [
        'timeout' => env('WHATSAPP_SESSION_TIMEOUT', 300), // 5 minutes
        'retry_attempts' => env('WHATSAPP_RETRY_ATTEMPTS', 3),
        'retry_delay' => env('WHATSAPP_RETRY_DELAY', 5000), // 5 seconds
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Media Configuration
    |--------------------------------------------------------------------------
    */
    
    'media' => [
        'max_file_size' => env('WHATSAPP_MAX_FILE_SIZE', 52428800), // 50MB
        'allowed_types' => [
            'image' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
            'video' => ['mp4', '3gp', 'mov', 'avi'],
            'audio' => ['mp3', 'ogg', 'wav', 'm4a', 'aac'],
            'document' => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt']
        ],
        'storage_path' => storage_path('app/whatsapp/media'),
        'public_url' => env('APP_URL') . '/storage/whatsapp/media',
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Webhook Configuration
    |--------------------------------------------------------------------------
    */
    
    'webhooks' => [
        'enabled' => env('WHATSAPP_WEBHOOKS_ENABLED', false),
        'url' => env('WHATSAPP_WEBHOOK_URL'),
        'secret' => env('WHATSAPP_WEBHOOK_SECRET'),
        'events' => [
            'message.received',
            'message.sent',
            'message.delivered',
            'message.read',
            'session.connected',
            'session.disconnected',
            'qr.updated'
        ]
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Database Configuration
    |--------------------------------------------------------------------------
    */
    
    'database' => [
        'store_messages' => env('WHATSAPP_STORE_MESSAGES', true),
        'store_contacts' => env('WHATSAPP_STORE_CONTACTS', true),
        'message_retention_days' => env('WHATSAPP_MESSAGE_RETENTION_DAYS', 30),
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    */
    
    'rate_limit' => [
        'enabled' => env('WHATSAPP_RATE_LIMIT_ENABLED', true),
        'messages_per_minute' => env('WHATSAPP_MESSAGES_PER_MINUTE', 20),
        'burst_limit' => env('WHATSAPP_BURST_LIMIT', 5),
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    */
    
    'logging' => [
        'enabled' => env('WHATSAPP_LOGGING_ENABLED', true),
        'level' => env('WHATSAPP_LOG_LEVEL', 'info'),
        'channel' => env('WHATSAPP_LOG_CHANNEL', 'single'),
    ]
];