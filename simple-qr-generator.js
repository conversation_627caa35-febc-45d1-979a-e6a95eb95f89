#!/usr/bin/env node

/**
 * Simple QR Code Generator for WhatsApp
 * 
 * This script creates a WhatsApp session and displays QR code using require() syntax
 */

const { makeWASocket, DisconnectReason, useMultiFileAuthState, Browsers, fetchLatestBaileysVersion } = require('baileys');
const QRCode = require('qrcode');
const pino = require('pino');
const fs = require('fs');
const path = require('path');

console.log('📱 Simple WhatsApp QR Generator');
console.log('==============================\n');

const logger = pino({ 
  level: 'info',
  transport: {
    target: 'pino-pretty',
    options: {
      colorize: true
    }
  }
});

async function generateQRCode() {
  try {
    console.log('🔄 Setting up WhatsApp connection...');
    
    // Create session directory
    const sessionDir = path.join(process.cwd(), 'temp_session');
    if (!fs.existsSync(sessionDir)) {
      fs.mkdirSync(sessionDir, { recursive: true });
    }
    
    // Get auth state
    const { state, saveCreds } = await useMultiFileAuthState(sessionDir);
    
    // Get latest version
    const { version, isLatest } = await fetchLatestBaileysVersion();
    console.log(`✅ Using Baileys version: ${version}, isLatest: ${isLatest}`);
    
    // Create socket
    const socket = makeWASocket({
      version,
      auth: {
        creds: state.creds,
        keys: state.keys
      },
      printQRInTerminal: true,
      logger,
      browser: Browsers.ubuntu('QR Generator'),
      generateHighQualityLinkPreview: false,
      syncFullHistory: false,
      markOnlineOnConnect: false
    });
    
    console.log('✅ Socket created successfully!');
    
    // Handle connection updates
    socket.ev.on('connection.update', async (update) => {
      const { connection, lastDisconnect, qr } = update;
      
      if (qr) {
        console.log('\n📱 QR CODE GENERATED!');
        console.log('====================');
        
        // Generate QR code data URL
        const qrDataURL = await QRCode.toDataURL(qr);
        console.log('🔗 QR Code Data URL:', qrDataURL.substring(0, 100) + '...');
        
        // Save QR code as image
        await QRCode.toFile('whatsapp-qr.png', qr);
        console.log('💾 QR code saved as: whatsapp-qr.png');
        
        console.log('\n📋 Instructions:');
        console.log('1. Open WhatsApp on your phone');
        console.log('2. Go to Settings > Linked Devices');
        console.log('3. Tap "Link a Device"');
        console.log('4. Scan the QR code displayed above or open whatsapp-qr.png');
        console.log('\n⏳ Waiting for device to scan...');
      }
      
      if (connection === 'open') {
        console.log('\n🎉 DEVICE CONNECTED SUCCESSFULLY!');
        console.log('================================');
        console.log('📞 Phone:', socket.user?.id || 'Unknown');
        console.log('👤 Name:', socket.user?.name || 'Unknown');
        console.log('\n✅ Your WhatsApp device is now connected!');
        
        // Clean up temp session
        setTimeout(() => {
          console.log('\n🧹 Cleaning up...');
          socket.end();
          if (fs.existsSync(sessionDir)) {
            fs.rmSync(sessionDir, { recursive: true, force: true });
          }
          process.exit(0);
        }, 5000);
      }
      
      if (connection === 'close') {
        const shouldReconnect = lastDisconnect?.error?.output?.statusCode !== DisconnectReason.loggedOut;
        console.log('\n❌ Connection closed');
        if (!shouldReconnect) {
          console.log('Device logged out');
        }
        
        // Clean up
        if (fs.existsSync(sessionDir)) {
          fs.rmSync(sessionDir, { recursive: true, force: true });
        }
        process.exit(shouldReconnect ? 1 : 0);
      }
    });
    
    // Handle credential updates
    socket.ev.on('creds.update', saveCreds);
    
    console.log('\n⏳ Waiting for QR code generation...');
    console.log('   Press Ctrl+C to stop\n');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n\n🛑 Shutting down...');
  process.exit(0);
});

// Start
generateQRCode();
