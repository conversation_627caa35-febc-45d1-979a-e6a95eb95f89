{"version": 3, "file": "verify-compliance.js", "sourceRoot": "", "sources": ["../verify-compliance.ts"], "names": [], "mappings": "AAAA,OAAO,YAAY,EAAE,EACnB,gBAAgB,EAChB,qBAAqB,EACrB,2BAA2B,EAC3B,QAAQ,EACR,yBAAyB,EACzB,cAAc,EACd,oBAAoB,EACpB,SAAS,EACT,KAAK,EACN,MAAM,SAAS,CAAC;AACjB,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAClC,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,SAAS,MAAM,YAAY,CAAC;AAEnC;;;;;GAKG;AAEH,MAAM,MAAM,GAAG,IAAI,CAAC;IAClB,KAAK,EAAE,MAAM;IACb,SAAS,EAAE;QACT,MAAM,EAAE,aAAa;QACrB,OAAO,EAAE;YACP,QAAQ,EAAE,IAAI;SACf;KACF;CACF,CAAC,CAAC;AAEH,KAAK,UAAU,uBAAuB;IACpC,MAAM,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;IAE9D,MAAM,MAAM,GAAG;QACb,OAAO,EAAE,KAAK;QACd,OAAO,EAAE,KAAK;QACd,SAAS,EAAE,KAAK;QAChB,MAAM,EAAE,KAAK;QACb,KAAK,EAAE,KAAK;QACZ,KAAK,EAAE,KAAK;QACZ,OAAO,EAAE,KAAK;QACd,MAAM,EAAE,KAAK;KACd,CAAC;IAEF,IAAI,CAAC;QACH,oCAAoC;QACpC,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACrC,IAAI,YAAY,IAAI,gBAAgB,IAAI,qBAAqB;YACzD,2BAA2B,IAAI,QAAQ;YACvC,yBAAyB,IAAI,cAAc,IAAI,oBAAoB;YACnE,SAAS,IAAI,KAAK,EAAE,CAAC;YACvB,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;YACtB,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QAClD,CAAC;QAED,6BAA6B;QAC7B,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;QAC9C,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,MAAM,yBAAyB,EAAE,CAAC;QAChE,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;YACtB,MAAM,CAAC,IAAI,CAAC,sBAAsB,OAAO,eAAe,QAAQ,EAAE,CAAC,CAAC;QACtE,CAAC;QAED,gCAAgC;QAChC,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QACzC,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,MAAM,qBAAqB,CAAC,WAAW,CAAC,CAAC;QACtE,IAAI,KAAK,IAAI,SAAS,EAAE,CAAC;YACvB,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;YACxB,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QAClD,CAAC;QAED,iDAAiD;QACjD,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;QAC9C,MAAM,MAAM,GAAG,YAAY,CAAC;YAC1B,OAAO;YACP,IAAI,EAAE;gBACJ,KAAK,EAAE,KAAK,CAAC,KAAK;gBAClB,IAAI,EAAE,2BAA2B,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC;aACtD;YACD,iBAAiB,EAAE,KAAK;YACxB,MAAM;YACN,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC,iBAAiB,CAAC;YAC3C,8BAA8B,EAAE,IAAI;YACpC,eAAe,EAAE,KAAK;YACtB,mBAAmB,EAAE,KAAK,EAAE,yBAAyB;SACtD,CAAC,CAAC;QAEH,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;YACrB,MAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YAE5C,2BAA2B;YAC3B,MAAM,MAAM,CAAC,GAAG,EAAE,CAAC;QACrB,CAAC;QAED,4DAA4D;QAC5D,MAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAC7C,MAAM,KAAK,GAAG,IAAI,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;QAClE,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC;YACpB,MAAM,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;QAC/D,CAAC;QAED,2BAA2B;QAC3B,MAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAC7C,MAAM,KAAK,GAAG,IAAI,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;QAClE,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC;YACpB,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QACjD,CAAC;QAED,kCAAkC;QAClC,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACpD,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAC9C,IAAI,aAAa,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChF,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;YACtB,MAAM,CAAC,IAAI,CAAC,qBAAqB,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC/D,CAAC;QAED,mBAAmB;QACnB,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACrC,IAAI,MAAM,IAAI,OAAO,MAAM,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YAChD,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;YACrB,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QACjD,CAAC;IAEH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;IACpD,CAAC;IAED,UAAU;IACV,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;IAC9C,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;IAEhD,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC;IAC/C,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;IAElE,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,EAAE;QACjD,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAClC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,KAAK,KAAK,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;IACjE,CAAC,CAAC,CAAC;IAEH,MAAM,CAAC,IAAI,CAAC,uBAAuB,YAAY,IAAI,WAAW,KAAK,IAAI,CAAC,KAAK,CAAC,YAAY,GAAC,WAAW,GAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAEjH,IAAI,YAAY,KAAK,WAAW,EAAE,CAAC;QACjC,MAAM,CAAC,IAAI,CAAC,6EAA6E,CAAC,CAAC;IAC7F,CAAC;SAAM,CAAC;QACN,MAAM,CAAC,IAAI,CAAC,sEAAsE,CAAC,CAAC;IACtF,CAAC;IAED,OAAO;QACL,MAAM,EAAE,YAAY,KAAK,WAAW;QACpC,KAAK,EAAE,GAAG,YAAY,IAAI,WAAW,EAAE;QACvC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,GAAC,WAAW,GAAC,GAAG,CAAC;QACpD,OAAO,EAAE,MAAM;KAChB,CAAC;AACJ,CAAC;AAED,kCAAkC;AAClC,KAAK,UAAU,sBAAsB;IACnC,MAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;IAElD,MAAM,QAAQ,GAAG;QACf,YAAY,EAAE,KAAK;QACnB,aAAa,EAAE,KAAK;QACpB,aAAa,EAAE,KAAK;QACpB,kBAAkB,EAAE,KAAK;QACzB,aAAa,EAAE,KAAK;KACrB,CAAC;IAEF,IAAI,CAAC;QACH,6BAA6B;QAC7B,MAAM,YAAY,GAAG;YACnB,cAAc,EAAE,qBAAqB,EAAE,cAAc;YACrD,cAAc,EAAE,cAAc,EAAE,iBAAiB;YACjD,gBAAgB,EAAE,iBAAiB,EAAE,gBAAgB;YACrD,qBAAqB,EAAE,iBAAiB;SACzC,CAAC;QAEF,IAAI,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,IAAI,KAAK,QAAQ,CAAC,EAAE,CAAC;YACzD,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAC;YAC7B,MAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAC/C,CAAC;QAED,iCAAiC;QACjC,IAAI,oBAAoB,IAAI,cAAc,EAAE,CAAC;YAC3C,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAC;YAC9B,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACtD,CAAC;QAED,uBAAuB;QACvB,IAAI,SAAS,EAAE,CAAC;YACd,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAC;YAC9B,MAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QACxD,CAAC;QAED,4BAA4B;QAC5B,MAAM,aAAa,GAAG,CAAC,WAAW,EAAE,aAAa,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;QACvF,IAAI,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,IAAI,KAAK,QAAQ,CAAC,EAAE,CAAC;YAC1D,QAAQ,CAAC,kBAAkB,GAAG,IAAI,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QACjD,CAAC;QAED,uBAAuB;QACvB,IAAI,IAAI,IAAI,gBAAgB,EAAE,CAAC;YAC7B,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAC;YAC9B,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACtD,CAAC;IAEH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;IAC3D,CAAC;IAED,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC;IACnD,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;IAEtE,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;IAC/C,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;IAE9C,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE,EAAE;QACrD,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAClC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,OAAO,KAAK,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;IACjF,CAAC,CAAC,CAAC;IAEH,MAAM,CAAC,IAAI,CAAC,uBAAuB,cAAc,IAAI,aAAa,KAAK,IAAI,CAAC,KAAK,CAAC,cAAc,GAAC,aAAa,GAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAEzH,OAAO;QACL,MAAM,EAAE,cAAc,KAAK,aAAa;QACxC,KAAK,EAAE,GAAG,cAAc,IAAI,aAAa,EAAE;QAC3C,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,GAAC,aAAa,GAAC,GAAG,CAAC;QACxD,OAAO,EAAE,QAAQ;KAClB,CAAC;AACJ,CAAC;AAED,mBAAmB;AACnB,KAAK,UAAU,uBAAuB;IACpC,MAAM,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;IAEtE,MAAM,gBAAgB,GAAG,MAAM,uBAAuB,EAAE,CAAC;IACzD,MAAM,cAAc,GAAG,MAAM,sBAAsB,EAAE,CAAC;IAEtD,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,gBAAgB,CAAC,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;IAE/F,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;IAC9C,MAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;IAC5C,MAAM,CAAC,IAAI,CAAC,kBAAkB,gBAAgB,CAAC,KAAK,KAAK,gBAAgB,CAAC,UAAU,IAAI,CAAC,CAAC;IAC1F,MAAM,CAAC,IAAI,CAAC,gBAAgB,cAAc,CAAC,KAAK,KAAK,cAAc,CAAC,UAAU,IAAI,CAAC,CAAC;IACpF,MAAM,CAAC,IAAI,CAAC,qBAAqB,YAAY,GAAG,CAAC,CAAC;IAElD,IAAI,YAAY,IAAI,EAAE,EAAE,CAAC;QACvB,MAAM,CAAC,IAAI,CAAC,qFAAqF,CAAC,CAAC;IACrG,CAAC;SAAM,IAAI,YAAY,IAAI,EAAE,EAAE,CAAC;QAC9B,MAAM,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAC;IAC3E,CAAC;SAAM,CAAC;QACN,MAAM,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAC;IAC3E,CAAC;IAED,OAAO;QACL,UAAU,EAAE,gBAAgB;QAC5B,QAAQ,EAAE,cAAc;QACxB,YAAY;KACb,CAAC;AACJ,CAAC;AAED,gCAAgC;AAChC,OAAO,EAAE,uBAAuB,EAAE,sBAAsB,EAAE,uBAAuB,EAAE,CAAC;AAEpF,2CAA2C;AAC3C,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,UAAU,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACpD,uBAAuB,EAAE;SACtB,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SAC3B,KAAK,CAAC,KAAK,CAAC,EAAE;QACb,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;QAC5C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;AACP,CAAC"}