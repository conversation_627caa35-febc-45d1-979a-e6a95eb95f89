{"version": 3, "file": "MediaHandler.js", "sourceRoot": "", "sources": ["../../gateway/MediaHandler.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,oBAAoB,EAAa,MAAM,SAAS,CAAC;AAC1D,OAAO,EAAE,MAAM,IAAI,CAAC;AACpB,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,MAAM,MAAM,QAAQ,CAAC;AAE5B,MAAM,OAAO,YAAY;IACf,QAAQ,CAAS;IAEzB;QACE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;QAC9D,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAC9B,CAAC;IAEO,oBAAoB;QAC1B,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QACnD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,OAAkB;QAC3C,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,oBAAoB,CACvC,OAAO,EACP,QAAQ,EACR,EAAE,EACF;gBACE,MAAM,EAAE;oBACN,KAAK,EAAE,QAAQ;oBACf,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAU,CAAA;iBACnC;gBACR,eAAe,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,EAAS,CAAC;aAClD,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO,IAAI,CAAC;YACd,CAAC;YAED,2BAA2B;YAC3B,MAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACnE,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YACjD,MAAM,QAAQ,GAAG,GAAG,IAAI,IAAI,SAAS,EAAE,CAAC;YACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAEpD,YAAY;YACZ,EAAE,CAAC,aAAa,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAEnC,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACjD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,OAAkB;QACzC,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,MAAM,cAAc,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC,WAA2C,CAAQ,CAAC;QAE7F,IAAI,cAAc,EAAE,QAAQ,EAAE,CAAC;YAC7B,MAAM,QAAQ,GAAG,cAAc,CAAC,QAAQ,CAAC;YAEzC,wCAAwC;YACxC,MAAM,SAAS,GAA8B;gBAC3C,YAAY,EAAE,KAAK;gBACnB,WAAW,EAAE,KAAK;gBAClB,WAAW,EAAE,KAAK;gBAClB,YAAY,EAAE,MAAM;gBACpB,WAAW,EAAE,KAAK;gBAClB,YAAY,EAAE,KAAK;gBACnB,iBAAiB,EAAE,KAAK;gBACxB,YAAY,EAAE,KAAK;gBACnB,WAAW,EAAE,KAAK;gBAClB,WAAW,EAAE,KAAK;gBAClB,WAAW,EAAE,KAAK;gBAClB,iBAAiB,EAAE,KAAK;gBACxB,oBAAoB,EAAE,KAAK;gBAC3B,yEAAyE,EAAE,MAAM;gBACjF,0BAA0B,EAAE,KAAK;gBACjC,mEAAmE,EAAE,MAAM;gBAC3E,YAAY,EAAE,KAAK;aACpB,CAAC;YAEF,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC;QACtC,CAAC;QAED,iCAAiC;QACjC,MAAM,SAAS,GAA8B;YAC3C,cAAc,EAAE,KAAK;YACrB,cAAc,EAAE,KAAK;YACrB,cAAc,EAAE,KAAK;YACrB,iBAAiB,EAAE,KAAK;YACxB,gBAAgB,EAAE,MAAM;SACzB,CAAC;QAEF,OAAO,SAAS,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC;IACzC,CAAC;IAED,YAAY,CAAC,QAAgB;QAC3B,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAC5C,CAAC;IAED,WAAW,CAAC,QAAgB,EAAE,OAAe;QAC3C,OAAO,GAAG,OAAO,mBAAmB,QAAQ,EAAE,CAAC;IACjD,CAAC;IAED,WAAW,CAAC,QAAgB;QAC1B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACpD,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC5B,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBACxB,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC9C,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,YAAY,CAAC,QAAgB;QAC3B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACpD,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC5B,MAAM,KAAK,GAAG,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBACpC,OAAO;oBACL,QAAQ;oBACR,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,OAAO,EAAE,KAAK,CAAC,SAAS;oBACxB,QAAQ,EAAE,KAAK,CAAC,KAAK;iBACtB,CAAC;YACJ,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;CACF"}