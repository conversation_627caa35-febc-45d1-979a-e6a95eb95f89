<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WhatsAppSession extends Model
{
    use HasFactory;

    protected $table = 'whatsapp_sessions';

    protected $fillable = [
        'session_id',
        'device_id',
        'status',
        'phone_number',
        'name',
        'qr_code',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    const STATUSES = [
        'connecting' => 'Connecting',
        'connected' => 'Connected',
        'disconnected' => 'Disconnected',
        'qr' => 'Waiting for QR Scan',
    ];

    /**
     * Get the device that owns this session.
     */
    public function device(): BelongsTo
    {
        return $this->belongsTo(WhatsAppDevice::class, 'device_id');
    }

    /**
     * Check if session is connected.
     */
    public function isConnected(): bool
    {
        return $this->status === 'connected';
    }

    /**
     * Check if session is waiting for QR scan.
     */
    public function isWaitingForQR(): bool
    {
        return $this->status === 'qr';
    }

    /**
     * Get status display name.
     */
    public function getStatusDisplayAttribute(): string
    {
        return self::STATUSES[$this->status] ?? 'Unknown';
    }

    /**
     * Get status badge class for UI.
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match ($this->status) {
            'connected' => 'bg-green-100 text-green-800',
            'connecting' => 'bg-blue-100 text-blue-800',
            'qr' => 'bg-yellow-100 text-yellow-800',
            'disconnected' => 'bg-gray-100 text-gray-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    /**
     * Scope to get only connected sessions.
     */
    public function scopeConnected($query)
    {
        return $query->where('status', 'connected');
    }

    /**
     * Scope to get sessions waiting for QR.
     */
    public function scopeWaitingForQR($query)
    {
        return $query->where('status', 'qr');
    }
}
