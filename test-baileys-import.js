#!/usr/bin/env node

/**
 * Test Baileys Import
 * 
 * This script tests what's actually available from the baileys package
 */

console.log('🔍 Testing Baileys Import...\n');

try {
  console.log('1. Testing require() import...');
  const baileys = require('baileys');
  console.log('✅ require() successful');
  console.log('Available exports:', Object.keys(baileys));
  console.log('makeWASocket type:', typeof baileys.makeWASocket);
  console.log('default export type:', typeof baileys.default);
  
  if (baileys.makeWASocket) {
    console.log('✅ makeWASocket is available via require()');
  } else if (baileys.default) {
    console.log('✅ default export is available via require()');
  } else {
    console.log('❌ Neither makeWASocket nor default export found');
  }
  
} catch (error) {
  console.log('❌ require() failed:', error.message);
}

console.log('\n2. Testing ES6 import...');

import('baileys').then(baileys => {
  console.log('✅ ES6 import successful');
  console.log('Available exports:', Object.keys(baileys));
  console.log('makeWASocket type:', typeof baileys.makeWASocket);
  console.log('default export type:', typeof baileys.default);
  
  if (baileys.makeWASocket) {
    console.log('✅ makeWASocket is available via ES6 import');
  } else if (baileys.default) {
    console.log('✅ default export is available via ES6 import');
  } else {
    console.log('❌ Neither makeWASocket nor default export found');
  }
  
  // Test creating a socket
  console.log('\n3. Testing socket creation...');
  try {
    const makeWASocket = baileys.makeWASocket || baileys.default;
    if (typeof makeWASocket === 'function') {
      console.log('✅ makeWASocket function is callable');
      console.log('Function signature:', makeWASocket.toString().substring(0, 100) + '...');
    } else {
      console.log('❌ makeWASocket is not a function');
    }
  } catch (error) {
    console.log('❌ Socket creation test failed:', error.message);
  }
  
}).catch(error => {
  console.log('❌ ES6 import failed:', error.message);
});
