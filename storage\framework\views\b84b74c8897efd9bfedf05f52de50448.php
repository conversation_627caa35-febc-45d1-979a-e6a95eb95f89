<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Device Management - WhatsApp Gateway</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen">
        <!-- Navigation -->
        <nav class="bg-green-600 text-white p-4">
            <div class="container mx-auto flex justify-between items-center">
                <h1 class="text-2xl font-bold">
                    <i class="fab fa-whatsapp mr-2"></i>
                    WhatsApp Gateway - Device Management
                </h1>
                <div class="space-x-4">
                    <a href="<?php echo e(route('whatsapp.dashboard')); ?>" class="hover:text-green-200">Dashboard</a>
                    <a href="<?php echo e(route('whatsapp.devices.index')); ?>" class="text-green-200">Device Management</a>
                    <a href="<?php echo e(route('whatsapp.sessions')); ?>" class="hover:text-green-200">Sessions</a>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="container mx-auto p-6">
            <!-- Header -->
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-3xl font-bold text-gray-800">Device Management</h2>
                <a href="<?php echo e(route('whatsapp.devices.create')); ?>" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition duration-200">
                    <i class="fas fa-plus mr-2"></i>Add New Device
                </a>
            </div>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                            <i class="fas fa-mobile-alt text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600">Total Devices</p>
                            <p class="text-2xl font-semibold"><?php echo e($devices->count()); ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 text-green-600">
                            <i class="fas fa-check-circle text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600">Connected</p>
                            <p class="text-2xl font-semibold text-green-600"><?php echo e($devices->where('status', 'connected')->count()); ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                            <i class="fas fa-clock text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600">Pending</p>
                            <p class="text-2xl font-semibold text-yellow-600"><?php echo e($devices->where('status', 'pending')->count()); ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-red-100 text-red-600">
                            <i class="fas fa-times-circle text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600">Disconnected</p>
                            <p class="text-2xl font-semibold text-red-600"><?php echo e($devices->where('status', 'disconnected')->count()); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Devices Table -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b">
                    <h3 class="text-xl font-semibold">Your Devices</h3>
                </div>
                <div class="p-6">
                    <?php if($devices->count() > 0): ?>
                        <div class="overflow-x-auto">
                            <table class="min-w-full table-auto">
                                <thead>
                                    <tr class="bg-gray-50">
                                        <th class="px-4 py-2 text-left">Device Name</th>
                                        <th class="px-4 py-2 text-left">Connection Method</th>
                                        <th class="px-4 py-2 text-left">Status</th>
                                        <th class="px-4 py-2 text-left">Phone Number</th>
                                        <th class="px-4 py-2 text-left">Display Name</th>
                                        <th class="px-4 py-2 text-left">Last Connected</th>
                                        <th class="px-4 py-2 text-left">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="devices-table">
                                    <?php $__currentLoopData = $devices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $device): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr class="border-b hover:bg-gray-50" data-device-id="<?php echo e($device->device_id); ?>">
                                            <td class="px-4 py-2 font-semibold"><?php echo e($device->device_name); ?></td>
                                            <td class="px-4 py-2">
                                                <span class="px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800">
                                                    <?php echo e($device->connection_method_display); ?>

                                                </span>
                                            </td>
                                            <td class="px-4 py-2">
                                                <span class="px-2 py-1 rounded-full text-xs <?php echo e($device->status_badge_class); ?>">
                                                    <?php echo e($device->status_display); ?>

                                                </span>
                                            </td>
                                            <td class="px-4 py-2"><?php echo e($device->phone_number ?? 'N/A'); ?></td>
                                            <td class="px-4 py-2"><?php echo e($device->display_name ?? 'N/A'); ?></td>
                                            <td class="px-4 py-2">
                                                <?php echo e($device->last_connected_at ? $device->last_connected_at->format('M d, Y H:i') : 'Never'); ?>

                                            </td>
                                            <td class="px-4 py-2">
                                                <div class="flex space-x-2">
                                                    <?php if($device->status === 'pending' || $device->status === 'disconnected'): ?>
                                                        <a href="<?php echo e(route('whatsapp.devices.connect', $device->device_id)); ?>" 
                                                           class="text-green-600 hover:text-green-800" title="Connect Device">
                                                            <i class="fas fa-plug"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    
                                                    <a href="<?php echo e(route('whatsapp.devices.show', $device->device_id)); ?>" 
                                                       class="text-blue-600 hover:text-blue-800" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    
                                                    <?php if($device->status === 'connected' && $device->session_id): ?>
                                                        <a href="<?php echo e(route('whatsapp.send.message', $device->session_id)); ?>" 
                                                           class="text-purple-600 hover:text-purple-800" title="Send Message">
                                                            <i class="fas fa-paper-plane"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    
                                                    <button onclick="deleteDevice('<?php echo e($device->device_id); ?>')" 
                                                            class="text-red-600 hover:text-red-800" title="Delete Device">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-12">
                            <i class="fas fa-mobile-alt text-6xl text-gray-400 mb-4"></i>
                            <h3 class="text-xl font-semibold text-gray-600 mb-2">No Devices Found</h3>
                            <p class="text-gray-500 mb-6">Get started by adding your first WhatsApp device.</p>
                            <a href="<?php echo e(route('whatsapp.devices.create')); ?>" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition duration-200">
                                <i class="fas fa-plus mr-2"></i>Add Your First Device
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Setup CSRF token for AJAX requests
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        // Socket.IO connection for real-time updates
        const socket = io('<?php echo e(config("whatsapp.socket_url", "http://localhost:3000")); ?>');

        socket.on('connect', () => {
            console.log('Connected to WhatsApp Gateway');
        });

        socket.on('session-status', (data) => {
            updateDeviceStatus(data.sessionId, data.status, data.info);
        });

        // Delete device function
        async function deleteDevice(deviceId) {
            if (!confirm('Are you sure you want to delete this device? This action cannot be undone.')) {
                return;
            }

            try {
                const response = await fetch(`/api/whatsapp/devices/${deviceId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': csrfToken,
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    location.reload();
                } else {
                    alert('Failed to delete device: ' + (result.message || 'Unknown error'));
                }
            } catch (error) {
                alert('Error deleting device: ' + error.message);
            }
        }

        // Update device status in real-time
        function updateDeviceStatus(sessionId, status, info) {
            // Find device row by session ID and update status
            const rows = document.querySelectorAll('[data-device-id]');
            rows.forEach(row => {
                // This would need to be enhanced to match session ID to device
                // For now, we'll reload the page on status changes
            });
        }

        // Auto-refresh page every 30 seconds to keep data current
        setInterval(() => {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
<?php /**PATH C:\laragon\www\larawamd\resources\views/whatsapp/devices/index.blade.php ENDPATH**/ ?>