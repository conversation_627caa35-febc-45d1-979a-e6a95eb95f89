v
import QRCode from 'qrcode';
import fs from 'fs';
import path from 'path';
import { DatabaseManager } from '../database/DatabaseManager';
import { MessageHandler } from './MessageHandler';
import { MediaHandler } from './MediaHandler';

export interface WhatsAppSession {
  sessionId: string;
  socket: WASocket | null;
  qr: string | null;
  status: 'connecting' | 'connected' | 'disconnected' | 'qr';
  info: any;
}

export class WhatsAppGateway {
  private sessions: Map<string, WhatsAppSession> = new Map();
  private io: Server;
  private dbManager: DatabaseManager;
  private messageHandler: MessageHandler;
  private mediaHandler: MediaHandler;
  private logger: any;
  private store: NodeCache;
  private groupCache: NodeCache;

  constructor(io: Server, dbManager: DatabaseManager) {
    this.io = io;
    this.dbManager = dbManager;
    this.messageHandler = new MessageHandler(dbManager);
    this.mediaHandler = new MediaHandler();
    
    // Initialize logger
    this.logger = pino({ 
      level: 'info',
      transport: {
        target: 'pino-pretty',
        options: {
          colorize: true
        }
      }
    });
    
    // Initialize in-memory store for better performance (using NodeCache)
    this.store = new NodeCache({ stdTTL: 5 * 60, useClones: false });
    
    // Initialize group metadata cache
    this.groupCache = new NodeCache({ stdTTL: 5 * 60, useClones: false });
    
    this.initializeExistingSessions();
  }

  private async initializeExistingSessions() {
    try {
      const sessions = await this.dbManager.getAllSessions();
      for (const session of sessions) {
        if (session.status === 'connected') {
          await this.createSession(session.session_id);
        }
      }
    } catch (error) {
      console.error('Error initializing existing sessions:', error);
    }
  }

  async createSession(sessionId: string): Promise<void> {
    if (this.sessions.has(sessionId)) {
      throw new Error(`Session ${sessionId} already exists`);
    }

    const sessionDir = path.join(process.cwd(), 'whatsapp', 'sessions', sessionId);
    
    // Ensure session directory exists
    if (!fs.existsSync(sessionDir)) {
      fs.mkdirSync(sessionDir, { recursive: true });
    }

    const session: WhatsAppSession = {
      sessionId,
      socket: null,
      qr: null,
      status: 'connecting',
      info: null
    };

    this.sessions.set(sessionId, session);

    try {
      const { state, saveCreds } = await useMultiFileAuthState(sessionDir);
      
      // Fetch latest Baileys version for better compatibility
      const { version, isLatest } = await fetchLatestBaileysVersion();
      this.logger.info(`Using Baileys version: ${version}, isLatest: ${isLatest}`);
      
      const socket = makeWASocket({
        version,
        auth: {
          creds: state.creds,
          keys: makeCacheableSignalKeyStore(state.keys, this.logger)
        },
        printQRInTerminal: false,
        logger: this.logger,
        browser: Browsers.ubuntu('WhatsApp Gateway'),
        generateHighQualityLinkPreview: true,
        syncFullHistory: false,
        markOnlineOnConnect: true,
        getMessage: async (key) => {
          if (this.store) {
            const msg = this.store.get(`${key.remoteJid}:${key.id}`);
            return msg?.message || undefined;
          }
          return undefined;
        },
        cachedGroupMetadata: async (jid) => this.groupCache.get(jid),
        shouldSyncHistoryMessage: msg => {
          return !!msg.message?.conversation || !!msg.message?.extendedTextMessage?.text;
        }
      });

      session.socket = socket;

      // Handle connection updates
      socket.ev.on('connection.update', async (update: Partial<ConnectionState>) => {
        const { connection, lastDisconnect, qr } = update;

        if (qr) {
          session.qr = await QRCode.toDataURL(qr);
          session.status = 'qr';
          this.io.emit('qr-updated', {
            sessionId,
            qr: session.qr
          });
          
          await this.dbManager.updateSession(sessionId, {
            status: 'qr',
            qr_code: session.qr
          });
        }

        if (connection === 'close') {
          const shouldReconnect = (lastDisconnect?.error as Boom)?.output?.statusCode !== DisconnectReason.loggedOut;
          
          if (shouldReconnect) {
            console.log(`Session ${sessionId} disconnected, reconnecting...`);
            session.status = 'connecting';
            this.io.emit('session-status', { sessionId, status: 'connecting' });
            
            // Retry connection after delay
            setTimeout(() => {
              this.createSession(sessionId).catch(console.error);
            }, 5000);
          } else {
            console.log(`Session ${sessionId} logged out`);
            session.status = 'disconnected';
            this.sessions.delete(sessionId);
            this.io.emit('session-status', { sessionId, status: 'disconnected' });
            
            await this.dbManager.updateSession(sessionId, {
              status: 'disconnected'
            });
          }
        } else if (connection === 'open') {
          console.log(`Session ${sessionId} connected successfully`);
          session.status = 'connected';
          session.info = socket.user;
          session.qr = null;
          
          this.io.emit('session-status', {
            sessionId,
            status: 'connected',
            info: session.info
          });
          
          await this.dbManager.updateSession(sessionId, {
            status: 'connected',
            phone_number: session.info?.id?.split(':')[0] || null,
            name: session.info?.name || null,
            qr_code: null
          });
        }
      });

      // Handle credential updates
      socket.ev.on('creds.update', saveCreds);

      // Handle incoming messages
      socket.ev.on('messages.upsert', async (messageUpdate) => {
        await this.handleIncomingMessages(sessionId, messageUpdate);
      });

      // Handle message updates (read receipts, etc.)
      socket.ev.on('messages.update', async (messageUpdates) => {
        for (const update of messageUpdates) {
          await this.messageHandler.handleMessageUpdate(sessionId, update);
        }
      });

      // Handle presence updates
      socket.ev.on('presence.update', (presenceUpdate) => {
        this.io.emit('presence-update', {
          sessionId,
          ...presenceUpdate
        });
      });

      // Handle group metadata caching
      socket.ev.on('groups.update', async (updates) => {
        for (const update of updates) {
          try {
            const metadata = await socket.groupMetadata(update.id);
            this.groupCache.set(update.id, metadata);
          } catch (error) {
            this.logger.error('Error updating group metadata cache:', error);
          }
        }
      });

      socket.ev.on('group-participants.update', async (update) => {
        try {
          const metadata = await socket.groupMetadata(update.id);
          this.groupCache.set(update.id, metadata);
        } catch (error) {
          this.logger.error('Error updating group participants cache:', error);
        }
      });

    } catch (error) {
      console.error(`Error creating session ${sessionId}:`, error);
      session.status = 'disconnected';
      this.io.emit('session-error', {
        sessionId,
        error: error.message
      });
      throw error;
    }
  }

  private async handleIncomingMessages(sessionId: string, messageUpdate: any) {
    const { messages, type } = messageUpdate;
    
    if (type !== 'notify') return;

    for (const message of messages) {
      try {
        // Skip if message is from status broadcast
        if (message.key.remoteJid === 'status@broadcast') continue;

        // Process and store message
        await this.messageHandler.processIncomingMessage(sessionId, message);

        // Emit to connected clients
        this.io.emit('message-received', {
          sessionId,
          message: await this.formatMessage(message)
        });

      } catch (error) {
        console.error('Error handling incoming message:', error);
      }
    }
  }

  private async formatMessage(message: WAMessage) {
    const messageType = getContentType(message.message);
    const messageContent = message.message?.[messageType as keyof proto.IMessage];
    
    return {
      id: message.key.id,
      from: message.key.remoteJid,
      fromMe: message.key.fromMe,
      timestamp: message.messageTimestamp,
      type: messageType,
      body: messageContent?.text || messageContent?.caption || '',
      hasMedia: ['imageMessage', 'videoMessage', 'audioMessage', 'documentMessage', 'stickerMessage'].includes(messageType || ''),
      participant: message.key.participant
    };
  }

  async sendMessage(sessionId: string, to: string, message: any): Promise<any> {
    const session = this.sessions.get(sessionId);
    
    if (!session || !session.socket || session.status !== 'connected') {
      throw new Error(`Session ${sessionId} is not connected`);
    }

    try {
      let sentMessage;
      
      // Validate JID format
      if (!to.includes('@')) {
        to = to.includes('-') ? `${to}@g.us` : `${to}@s.whatsapp.net`;
      }
      
      if (message.type === 'text') {
        sentMessage = await session.socket.sendMessage(to, { 
          text: message.text 
        });
      } else if (message.type === 'image') {
        const imageMessage: any = {
          image: message.url ? { url: message.url } : message.buffer,
        };
        if (message.caption) imageMessage.caption = message.caption;
        if (message.viewOnce) imageMessage.viewOnce = true;
        
        sentMessage = await session.socket.sendMessage(to, imageMessage);
      } else if (message.type === 'video') {
        const videoMessage: any = {
          video: message.url ? { url: message.url } : message.buffer,
        };
        if (message.caption) videoMessage.caption = message.caption;
        if (message.viewOnce) videoMessage.viewOnce = true;
        if (message.gifPlayback) videoMessage.gifPlayback = true;
        if (message.ptv) videoMessage.ptv = true;
        
        sentMessage = await session.socket.sendMessage(to, videoMessage);
      } else if (message.type === 'audio') {
        const audioMessage: any = {
          audio: message.url ? { url: message.url } : message.buffer,
          mimetype: message.mimetype || 'audio/mp4'
        };
        if (message.ptt) audioMessage.ptt = true;
        
        sentMessage = await session.socket.sendMessage(to, audioMessage);
      } else if (message.type === 'document') {
        sentMessage = await session.socket.sendMessage(to, {
          document: message.url ? { url: message.url } : message.buffer,
          fileName: message.fileName || 'document',
          mimetype: message.mimetype || 'application/octet-stream'
        });
      } else if (message.type === 'sticker') {
        sentMessage = await session.socket.sendMessage(to, {
          sticker: message.url ? { url: message.url } : message.buffer
        });
      } else if (message.type === 'location') {
        sentMessage = await session.socket.sendMessage(to, {
          location: {
            degreesLatitude: message.latitude,
            degreesLongitude: message.longitude
          }
        });
      } else if (message.type === 'contact') {
        sentMessage = await session.socket.sendMessage(to, {
          contacts: {
            displayName: message.displayName,
            contacts: message.contacts
          }
        });
      } else if (message.type === 'poll') {
        sentMessage = await session.socket.sendMessage(to, {
          poll: {
            name: message.name,
            values: message.values,
            selectableCount: message.selectableCount || 1
          }
        });
      } else {
        throw new Error(`Unsupported message type: ${message.type}`);
      }

      // Store sent message
      await this.messageHandler.storeSentMessage(sessionId, to, message, sentMessage);

      return {
        success: true,
        messageId: sentMessage.key.id,
        timestamp: sentMessage.messageTimestamp
      };

    } catch (error) {
      this.logger.error('Error sending message:', error);
      throw error;
    }
  }

  async getSessionStatus(sessionId: string): Promise<WhatsAppSession | null> {
    return this.sessions.get(sessionId) || null;
  }

  async getAllSessions(): Promise<WhatsAppSession[]> {
    return Array.from(this.sessions.values());
  }

  async deleteSession(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    
    if (session && session.socket) {
      await session.socket.logout();
    }
    
    this.sessions.delete(sessionId);
    
    // Delete session files
    const sessionDir = path.join(process.cwd(), 'whatsapp', 'sessions', sessionId);
    if (fs.existsSync(sessionDir)) {
      fs.rmSync(sessionDir, { recursive: true, force: true });
    }
    
    // Update database
    await this.dbManager.updateSession(sessionId, {
      status: 'disconnected'
    });
  }

  async downloadMedia(sessionId: string, messageId: string): Promise<Buffer | null> {
    const session = this.sessions.get(sessionId);
    
    if (!session || !session.socket) {
      throw new Error(`Session ${sessionId} is not available`);
    }

    try {
      const message = await this.dbManager.getMessage(messageId);
      if (!message) {
        throw new Error('Message not found');
      }

      // This would need the actual WAMessage object to download media
      // In a real implementation, you'd store the message object or reconstruct it
      return null;
    } catch (error) {
      console.error('Error downloading media:', error);
      throw error;
    }
  }

  async cleanup(): Promise<void> {
    console.log('Cleaning up WhatsApp Gateway...');
    
    for (const [sessionId, session] of this.sessions) {
      try {
        if (session.socket) {
          await session.socket.end();
        }
      } catch (error) {
        console.error(`Error cleaning up session ${sessionId}:`, error);
      }
    }
    
    this.sessions.clear();
  }
}