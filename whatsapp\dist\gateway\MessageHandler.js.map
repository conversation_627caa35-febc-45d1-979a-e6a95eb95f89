{"version": 3, "file": "MessageHandler.js", "sourceRoot": "", "sources": ["../../gateway/MessageHandler.ts"], "names": [], "mappings": "AAAA,OAAO,EAAa,cAAc,EAAS,SAAS,EAAE,MAAM,SAAS,CAAC;AAGtE,MAAM,OAAO,cAAc;IACjB,SAAS,CAAkB;IAEnC,YAAY,SAA0B;QACpC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,SAAiB,EAAE,OAAkB;QAChE,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACpD,MAAM,cAAc,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC,WAAmC,CAAC,CAAC;YAE9E,MAAM,WAAW,GAAG;gBAClB,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,EAAG;gBAC3B,UAAU,EAAE,SAAS;gBACrB,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,SAAU;gBAChC,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,KAAK;gBACpC,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,IAAI;gBAC5C,YAAY,EAAE,WAAW,IAAI,SAAS;gBACtC,YAAY,EAAE,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC;gBACrD,SAAS,EAAE,IAAI;gBACf,UAAU,EAAE,IAAI;gBAChB,UAAU,EAAE,IAAI;gBAChB,SAAS,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC;gBAC5D,MAAM,EAAE,UAAU;gBAClB,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;aACrC,CAAC;YAEF,wBAAwB;YACxB,IAAI,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE,CAAC;gBACrC,WAAW,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;gBACxD,WAAW,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;YAC7D,CAAC;YAED,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YAE/C,qCAAqC;YACrC,MAAM,IAAI,CAAC,sBAAsB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAExD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,SAAiB,EAAE,EAAU,EAAE,WAAgB,EAAE,WAAgB;QACtF,IAAI,CAAC;YACH,MAAM,aAAa,GAAG;gBACpB,UAAU,EAAE,WAAW,CAAC,GAAG,CAAC,EAAE;gBAC9B,UAAU,EAAE,SAAS;gBACrB,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,IAAI;gBACb,WAAW,EAAE,IAAI;gBACjB,YAAY,EAAE,WAAW,CAAC,IAAI;gBAC9B,YAAY,EAAE,WAAW,CAAC,IAAI,IAAI,WAAW,CAAC,OAAO,IAAI,IAAI;gBAC7D,SAAS,EAAE,WAAW,CAAC,GAAG,IAAI,IAAI;gBAClC,UAAU,EAAE,WAAW,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI;gBACjE,UAAU,EAAE,IAAI;gBAChB,SAAS,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC;gBAChE,MAAM,EAAE,MAAM;gBACd,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;aACzC,CAAC;YAEF,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;QACnD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,SAAiB,EAAE,MAAW;QACtD,IAAI,CAAC;YACH,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,MAAM,CAAC;YAE9C,IAAI,aAAa,CAAC,MAAM,EAAE,CAAC;gBACzB,MAAM,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,GAAG,CAAC,EAAE,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;YACzE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAEO,kBAAkB,CAAC,cAAmB;QAC5C,IAAI,CAAC,cAAc;YAAE,OAAO,IAAI,CAAC;QAEjC,iCAAiC;QACjC,IAAI,cAAc,CAAC,IAAI;YAAE,OAAO,cAAc,CAAC,IAAI,CAAC;QACpD,IAAI,cAAc,CAAC,OAAO;YAAE,OAAO,cAAc,CAAC,OAAO,CAAC;QAC1D,IAAI,cAAc,CAAC,YAAY;YAAE,OAAO,cAAc,CAAC,YAAY,CAAC;QACpE,IAAI,cAAc,CAAC,mBAAmB,EAAE,IAAI;YAAE,OAAO,cAAc,CAAC,mBAAmB,CAAC,IAAI,CAAC;QAE7F,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,cAAc,CAAC,WAA0B;QAC/C,IAAI,CAAC,WAAW;YAAE,OAAO,KAAK,CAAC;QAE/B,MAAM,UAAU,GAAG;YACjB,cAAc;YACd,cAAc;YACd,cAAc;YACd,iBAAiB;YACjB,gBAAgB;SACjB,CAAC;QAEF,OAAO,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;IAC1C,CAAC;IAEO,YAAY,CAAC,WAA0B;QAC7C,IAAI,CAAC,WAAW;YAAE,OAAO,IAAI,CAAC;QAE9B,MAAM,OAAO,GAA8B;YACzC,cAAc,EAAE,OAAO;YACvB,cAAc,EAAE,OAAO;YACvB,cAAc,EAAE,OAAO;YACvB,iBAAiB,EAAE,UAAU;YAC7B,gBAAgB,EAAE,SAAS;SAC5B,CAAC;QAEF,OAAO,OAAO,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC;IACtC,CAAC;IAEO,YAAY,CAAC,cAAmB;QACtC,IAAI,CAAC,cAAc;YAAE,OAAO,IAAI,CAAC;QAEjC,OAAO,cAAc,CAAC,UAAU,IAAI,IAAI,CAAC;IAC3C,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,SAAiB,EAAE,OAAkB;QACxE,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,SAAU,CAAC;YACnC,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC;YAE5C,uCAAuC;YACvC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC3B,MAAM,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;gBAC/B,IAAI,OAAO,EAAE,CAAC;oBACZ,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;wBAChC,UAAU,EAAE,SAAS;wBACrB,GAAG,EAAE,GAAG;wBACR,YAAY,EAAE,OAAO,CAAC,IAAI;wBAC1B,IAAI,EAAE,OAAO,CAAC,QAAQ,IAAI,IAAI;wBAC9B,QAAQ,EAAE,KAAK;wBACf,SAAS,EAAE,IAAI,IAAI,EAAE;qBACtB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,kCAAkC;YAClC,IAAI,WAAW,EAAE,CAAC;gBAChB,MAAM,OAAO,GAAG,SAAS,CAAC,WAAW,CAAC,CAAC;gBACvC,IAAI,OAAO,EAAE,CAAC;oBACZ,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;wBAChC,UAAU,EAAE,SAAS;wBACrB,GAAG,EAAE,WAAW;wBAChB,YAAY,EAAE,OAAO,CAAC,IAAI;wBAC1B,IAAI,EAAE,OAAO,CAAC,QAAQ,IAAI,IAAI;wBAC9B,QAAQ,EAAE,KAAK;wBACf,SAAS,EAAE,IAAI,IAAI,EAAE;qBACtB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,2CAA2C;YAC3C,IAAI,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC1B,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;oBAChC,UAAU,EAAE,SAAS;oBACrB,GAAG,EAAE,GAAG;oBACR,YAAY,EAAE,IAAI;oBAClB,IAAI,EAAE,IAAI,EAAE,iDAAiD;oBAC7D,QAAQ,EAAE,IAAI;oBACd,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAC;YACL,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;CACF"}