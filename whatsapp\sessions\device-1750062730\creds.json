{"noiseKey": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "4O763H0Z3+Fu6k652mXvUa9OQLsysWniXyzdooO/Tmk="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "x2Ow3bxxtKfFNYCmxuwoW88d07ihpOLPOGXobQeEvyg="}}, "pairingEphemeralKeyPair": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "kCE9di4oaq5iN0J2OmL1QLC2ub7ZxX3LJHZMafRujXg="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "+0XOO8JWz4B7vkJ+lovRNbU95IfPLXSB/DhBw9Gyazg="}}, "signedIdentityKey": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "+AbrqWr+z68r+0AU3TmFDzyil9oYX3+KcH8DDr7PZGM="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "ofRQftbRa4bTODqHht9lyWuNkoO5VjPQD9vJgz7FeWA="}}, "signedPreKey": {"keyPair": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "2OCZNh5lA+3hqAFbiViqCpKB+dSmkjjv+K4hER9vp04="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "xoTZPKfvg/4YINfJqRAn3bPw/IqIcj92IbhnfJhTrgc="}}, "signature": {"type": "<PERSON><PERSON><PERSON>", "data": "etlv0ZZBGp0xaQtXbIIkX/dSDGsW+QGk/GP0FJV2G32/NHMvtB9L4Av9Wy7iy42syQ5BQplYgVZDuLcG/GE6Dw=="}, "keyId": 1}, "registrationId": 12, "advSecretKey": "E+iT6Shd2VnrjxGNrBWh9oJKC8XHp2WLRxV2L6BRisk=", "processedHistoryMessages": [], "nextPreKeyId": 1, "firstUnuploadedPreKeyId": 1, "accountSyncCounter": 0, "accountSettings": {"unarchiveChats": false}, "registered": false, "account": {"details": "CPCxn4ABEIuuv8IGGBIgACgA", "accountSignatureKey": "iKRPUHOAwB3mA9TQ1wt0UnwrJX+xiwfog4sHvMDlfmI=", "accountSignature": "xpPmF8GnYMPQcE0i3rTjzBuuZ2fuy0w1zzQdyNZFK16A8U6W9pAi3pALz7Hz3qMm1Lp8ShI1dLJICM5Riw91Aw==", "deviceSignature": "20TuKo7tv/rO81Lk/onJUgRrlB9HFGE0zwqthJsDyQrF/eFx2kmCfKjtIgCsuLg1c5kJ76rokS130NrRKhSgCA=="}, "me": {"id": "*************:<EMAIL>"}, "signalIdentities": [{"identifier": {"name": "*************:<EMAIL>", "deviceId": 0}, "identifierKey": {"type": "<PERSON><PERSON><PERSON>", "data": "BYikT1BzgMAd5gPU0NcLdFJ8KyV/sYsH6IOLB7zA5X5i"}}], "platform": "android"}