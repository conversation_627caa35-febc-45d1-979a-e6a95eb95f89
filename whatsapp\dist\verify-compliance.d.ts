declare function verifyBaileysCompliance(): Promise<{
    passed: boolean;
    score: string;
    percentage: number;
    details: {
        imports: boolean;
        version: boolean;
        authState: boolean;
        socket: boolean;
        store: boolean;
        cache: boolean;
        browser: boolean;
        logger: boolean;
    };
}>;
declare function verifyAdvancedFeatures(): Promise<{
    passed: boolean;
    score: string;
    percentage: number;
    details: {
        messageTypes: boolean;
        mediaHandling: boolean;
        groupFeatures: boolean;
        presenceManagement: boolean;
        errorHandling: boolean;
    };
}>;
declare function runCompleteVerification(): Promise<{
    compliance: {
        passed: boolean;
        score: string;
        percentage: number;
        details: {
            imports: boolean;
            version: boolean;
            authState: boolean;
            socket: boolean;
            store: boolean;
            cache: boolean;
            browser: boolean;
            logger: boolean;
        };
    };
    features: {
        passed: boolean;
        score: string;
        percentage: number;
        details: {
            messageTypes: boolean;
            mediaHandling: boolean;
            groupFeatures: boolean;
            presenceManagement: boolean;
            errorHandling: boolean;
        };
    };
    overallScore: number;
}>;
export { verifyBaileysCompliance, verifyAdvancedFeatures, runCompleteVerification };
//# sourceMappingURL=verify-compliance.d.ts.map