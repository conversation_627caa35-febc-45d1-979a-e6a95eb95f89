<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class WhatsAppDevice extends Model
{
    use HasFactory;

    protected $table = 'whatsapp_devices';

    protected $fillable = [
        'device_name',
        'device_id',
        'connection_method',
        'status',
        'phone_number',
        'display_name',
        'session_id',
        'qr_code',
        'auth_code',
        'last_connected_at',
    ];

    protected $casts = [
        'last_connected_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    const CONNECTION_METHODS = [
        'qr_code' => 'QR Code Scanning',
        'auth_code' => 'Authentication Code',
    ];

    const STATUSES = [
        'pending' => 'Pending Connection',
        'connecting' => 'Connecting',
        'connected' => 'Connected',
        'disconnected' => 'Disconnected',
        'failed' => 'Connection Failed',
    ];

    /**
     * Get the sessions for this device.
     */
    public function sessions(): HasMany
    {
        return $this->hasMany(WhatsAppSession::class, 'device_id');
    }

    /**
     * Get the current active session for this device.
     */
    public function currentSession()
    {
        return $this->sessions()->where('status', 'connected')->first();
    }

    /**
     * Check if device is currently connected.
     */
    public function isConnected(): bool
    {
        return $this->status === 'connected';
    }

    /**
     * Check if device is pending connection.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if device is connecting.
     */
    public function isConnecting(): bool
    {
        return $this->status === 'connecting';
    }

    /**
     * Get connection method display name.
     */
    public function getConnectionMethodDisplayAttribute(): string
    {
        return self::CONNECTION_METHODS[$this->connection_method] ?? 'Unknown';
    }

    /**
     * Get status display name.
     */
    public function getStatusDisplayAttribute(): string
    {
        return self::STATUSES[$this->status] ?? 'Unknown';
    }

    /**
     * Get status badge class for UI.
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match ($this->status) {
            'connected' => 'bg-green-100 text-green-800',
            'connecting' => 'bg-blue-100 text-blue-800',
            'pending' => 'bg-yellow-100 text-yellow-800',
            'failed' => 'bg-red-100 text-red-800',
            'disconnected' => 'bg-gray-100 text-gray-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    /**
     * Scope to get only connected devices.
     */
    public function scopeConnected($query)
    {
        return $query->where('status', 'connected');
    }

    /**
     * Scope to get only pending devices.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get devices by connection method.
     */
    public function scopeByConnectionMethod($query, string $method)
    {
        return $query->where('connection_method', $method);
    }
}
