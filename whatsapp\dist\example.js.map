{"version": 3, "file": "example.js", "sourceRoot": "", "sources": ["../example.ts"], "names": [], "mappings": "AAAA,OAAO,YAAY,EAAE,EACnB,gBAAgB,EAChB,qBAAqB,EACrB,2BAA2B,EAC3B,QAAQ,EACR,iBAAiB,EACjB,yBAAyB,EACzB,cAAc,EACd,oBAAoB,EACrB,MAAM,SAAS,CAAC;AAEjB,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,SAAS,MAAM,YAAY,CAAC;AAEnC,oBAAoB;AACpB,MAAM,MAAM,GAAG,IAAI,CAAC;IAClB,KAAK,EAAE,MAAM;IACb,SAAS,EAAE;QACT,MAAM,EAAE,aAAa;QACrB,OAAO,EAAE;YACP,QAAQ,EAAE,IAAI;SACf;KACF;CACF,CAAC,CAAC;AAEH,6BAA6B;AAC7B,MAAM,KAAK,GAAG,iBAAiB,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;AAE5C,yBAAyB;AACzB,MAAM,UAAU,GAAG,IAAI,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;AAEvE,KAAK,UAAU,iBAAiB;IAC9B,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,MAAM,qBAAqB,CAAC,mBAAmB,CAAC,CAAC;IAE9E,+BAA+B;IAC/B,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,MAAM,yBAAyB,EAAE,CAAC;IAChE,MAAM,CAAC,IAAI,CAAC,0BAA0B,OAAO,eAAe,QAAQ,EAAE,CAAC,CAAC;IAExE,MAAM,IAAI,GAAG,YAAY,CAAC;QACxB,OAAO;QACP,IAAI,EAAE;YACJ,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,IAAI,EAAE,2BAA2B,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC;SACtD;QACD,iBAAiB,EAAE,IAAI;QACvB,MAAM;QACN,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC,kBAAkB,CAAC;QAC5C,8BAA8B,EAAE,IAAI;QACpC,eAAe,EAAE,KAAK;QACtB,mBAAmB,EAAE,IAAI;QACzB,UAAU,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE;YACxB,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,SAAU,EAAE,GAAG,CAAC,EAAG,CAAC,CAAC;gBAC7D,OAAO,GAAG,EAAE,OAAO,IAAI,SAAS,CAAC;YACnC,CAAC;YACD,OAAO,SAAS,CAAC;QACnB,CAAC;QACD,mBAAmB,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC;QACvD,wBAAwB,EAAE,GAAG,CAAC,EAAE;YAC9B,OAAO,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,YAAY,IAAI,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,mBAAmB,EAAE,IAAI,CAAC;QACjF,CAAC;KACF,CAAC,CAAC;IAEH,uBAAuB;IACvB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAEpB,4BAA4B;IAC5B,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,MAAM,EAAE,EAAE;QACzC,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,GAAG,MAAM,CAAC;QAE9C,IAAI,UAAU,KAAK,OAAO,EAAE,CAAC;YAC3B,MAAM,eAAe,GAAI,cAAc,EAAE,KAAc,EAAE,MAAM,EAAE,UAAU,KAAK,gBAAgB,CAAC,SAAS,CAAC;YAC3G,MAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE,cAAc,EAAE,KAAK,EAAE,gBAAgB,EAAE,eAAe,CAAC,CAAC;YAElG,IAAI,eAAe,EAAE,CAAC;gBACpB,iBAAiB,EAAE,CAAC;YACtB,CAAC;QACH,CAAC;aAAM,IAAI,UAAU,KAAK,MAAM,EAAE,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;QAChD,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,2BAA2B;IAC3B,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,iBAAiB,EAAE,KAAK,EAAE,aAAa,EAAE,EAAE;QACpD,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,aAAa,CAAC;QAEzC,IAAI,IAAI,KAAK,QAAQ;YAAE,OAAO;QAE9B,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,IAAI,CAAC,OAAO,CAAC,OAAO;gBAAE,SAAS;YAC/B,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM;gBAAE,SAAS,CAAC,oBAAoB;YACtD,IAAI,OAAO,CAAC,GAAG,CAAC,SAAS,KAAK,kBAAkB;gBAAE,SAAS,CAAC,sBAAsB;YAElF,MAAM,WAAW,GAAG,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACpD,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,SAAU,CAAC;YACpC,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,YAAY;gBAC5B,OAAO,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI;gBACzC,OAAO,CAAC,OAAO,CAAC,WAA2C,CAAC,EAAE,OAAO,IAAI,EAAE,CAAC;YAEzF,MAAM,CAAC,IAAI,CAAC,YAAY,WAAW,SAAS,IAAI,KAAK,IAAI,EAAE,CAAC,CAAC;YAE7D,8BAA8B;YAC9B,IAAI,WAAW,KAAK,cAAc,IAAI,WAAW,KAAK,qBAAqB,EAAE,CAAC;gBAC5E,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,SAAS,IAAI,EAAE,EAAE,CAAC,CAAC;YAC1D,CAAC;YAED,iCAAiC;YACjC,IAAI,CAAC,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC9F,IAAI,CAAC;oBACH,MAAM,MAAM,GAAG,MAAM,oBAAoB,CACvC,OAAO,EACP,QAAQ,EACR,EAAE,EACF;wBACE,MAAM;wBACN,eAAe,EAAE,IAAI,CAAC,kBAAkB;qBACzC,CACF,CAAC;oBAEF,IAAI,MAAM,EAAE,CAAC;wBACX,MAAM,CAAC,IAAI,CAAC,cAAc,WAAW,aAAa,MAAM,CAAC,MAAM,QAAQ,CAAC,CAAC;wBACzE,wDAAwD;oBAC1D,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;gBAClD,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,gCAAgC;IAChC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,eAAe,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;QAC5C,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBACrD,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;gBACpC,MAAM,CAAC,IAAI,CAAC,+BAA+B,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;YACjE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACxD,CAAC;QACH,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,2BAA2B,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE;QACvD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACrD,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;YACpC,MAAM,CAAC,IAAI,CAAC,mCAAmC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;QACrE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,4BAA4B;IAC5B,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;IAEtC,0BAA0B;IAC1B,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,iBAAiB,EAAE,CAAC,cAAc,EAAE,EAAE;QAC/C,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC;IAClD,CAAC,CAAC,CAAC;IAEH,gDAAgD;IAChD,MAAM,QAAQ,GAAG;QACf,oBAAoB;QACpB,QAAQ,EAAE,KAAK,EAAE,GAAW,EAAE,IAAY,EAAE,EAAE;YAC5C,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;QAC/C,CAAC;QAED,0BAA0B;QAC1B,SAAS,EAAE,KAAK,EAAE,GAAW,EAAE,QAAgB,EAAE,OAAgB,EAAE,EAAE;YACnE,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE;gBACjC,KAAK,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE;gBACxB,OAAO;aACR,CAAC,CAAC;QACL,CAAC;QAED,aAAa;QACb,SAAS,EAAE,KAAK,EAAE,GAAW,EAAE,QAAgB,EAAE,OAAgB,EAAE,EAAE;YACnE,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE;gBACjC,KAAK,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE;gBACxB,OAAO;aACR,CAAC,CAAC;QACL,CAAC;QAED,aAAa;QACb,SAAS,EAAE,KAAK,EAAE,GAAW,EAAE,QAAgB,EAAE,EAAE;YACjD,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE;gBACjC,KAAK,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE;gBACxB,QAAQ,EAAE,WAAW;aACtB,CAAC,CAAC;QACL,CAAC;QAED,gBAAgB;QAChB,YAAY,EAAE,KAAK,EAAE,GAAW,EAAE,WAAmB,EAAE,QAAgB,EAAE,EAAE;YACzE,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE;gBACjC,QAAQ,EAAE,EAAE,GAAG,EAAE,WAAW,EAAE;gBAC9B,QAAQ;gBACR,QAAQ,EAAE,iBAAiB;aAC5B,CAAC,CAAC;QACL,CAAC;QAED,gBAAgB;QAChB,YAAY,EAAE,KAAK,EAAE,GAAW,EAAE,QAAgB,EAAE,SAAiB,EAAE,EAAE;YACvE,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE;gBACjC,QAAQ,EAAE;oBACR,eAAe,EAAE,QAAQ;oBACzB,gBAAgB,EAAE,SAAS;iBAC5B;aACF,CAAC,CAAC;QACL,CAAC;QAED,YAAY;QACZ,QAAQ,EAAE,KAAK,EAAE,GAAW,EAAE,IAAY,EAAE,MAAgB,EAAE,EAAE;YAC9D,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE;gBACjC,IAAI,EAAE;oBACJ,IAAI;oBACJ,MAAM;oBACN,eAAe,EAAE,CAAC;iBACnB;aACF,CAAC,CAAC;QACL,CAAC;QAED,gBAAgB;QAChB,YAAY,EAAE,KAAK,EAAE,GAAW,EAAE,UAAe,EAAE,KAAa,EAAE,EAAE;YAClE,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE;gBACjC,KAAK,EAAE;oBACL,IAAI,EAAE,KAAK;oBACX,GAAG,EAAE,UAAU;iBAChB;aACF,CAAC,CAAC;QACL,CAAC;QAED,qCAAqC;QACrC,WAAW,EAAE,KAAK,EAAE,MAAc,EAAE,EAAE;YACpC,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YAC/C,OAAO,MAAM,EAAE,MAAM,CAAC;QACxB,CAAC;QAED,sBAAsB;QACtB,iBAAiB,EAAE,KAAK,EAAE,GAAW,EAAE,EAAE;YACvC,IAAI,CAAC;gBACH,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YACpD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,kBAAkB;QAClB,cAAc,EAAE,KAAK,EAAE,GAAW,EAAE,QAA4E,EAAE,EAAE;YAClH,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QACtD,CAAC;KACF,CAAC;IAEF,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;AAC5B,CAAC;AAED,uBAAuB;AACvB,iBAAiB,EAAE;KAChB,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE;IAC3B,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;IAE/C,iBAAiB;IACjB,yEAAyE;IACzE,oGAAoG;AACtG,CAAC,CAAC;KACD,KAAK,CAAC,KAAK,CAAC,EAAE;IACb,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;AAC5C,CAAC,CAAC,CAAC;AAEL,eAAe,iBAAiB,CAAC"}