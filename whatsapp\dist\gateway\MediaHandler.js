import { downloadMediaMessage } from 'baileys';
import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
export class MediaHandler {
    mediaDir;
    constructor() {
        this.mediaDir = path.join(process.cwd(), 'whatsapp', 'media');
        this.ensureMediaDirectory();
    }
    ensureMediaDirectory() {
        if (!fs.existsSync(this.mediaDir)) {
            fs.mkdirSync(this.mediaDir, { recursive: true });
        }
    }
    async downloadAndSaveMedia(message) {
        try {
            const buffer = await downloadMediaMessage(message, 'buffer', {}, {
                logger: {
                    level: 'silent',
                    child: () => ({ level: 'silent' })
                },
                reuploadRequest: () => Promise.resolve({})
            });
            if (!buffer) {
                return null;
            }
            // Generate unique filename
            const hash = crypto.createHash('md5').update(buffer).digest('hex');
            const extension = this.getFileExtension(message);
            const filename = `${hash}.${extension}`;
            const filepath = path.join(this.mediaDir, filename);
            // Save file
            fs.writeFileSync(filepath, buffer);
            return filename;
        }
        catch (error) {
            console.error('Error downloading media:', error);
            return null;
        }
    }
    getFileExtension(message) {
        const messageType = Object.keys(message.message || {})[0];
        const messageContent = message.message?.[messageType];
        if (messageContent?.mimetype) {
            const mimeType = messageContent.mimetype;
            // Common mime type to extension mapping
            const mimeToExt = {
                'image/jpeg': 'jpg',
                'image/png': 'png',
                'image/gif': 'gif',
                'image/webp': 'webp',
                'video/mp4': 'mp4',
                'video/3gpp': '3gp',
                'video/quicktime': 'mov',
                'audio/mpeg': 'mp3',
                'audio/ogg': 'ogg',
                'audio/wav': 'wav',
                'audio/mp4': 'm4a',
                'application/pdf': 'pdf',
                'application/msword': 'doc',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
                'application/vnd.ms-excel': 'xls',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
                'text/plain': 'txt'
            };
            return mimeToExt[mimeType] || 'bin';
        }
        // Fallback based on message type
        const typeToExt = {
            'imageMessage': 'jpg',
            'videoMessage': 'mp4',
            'audioMessage': 'mp3',
            'documentMessage': 'pdf',
            'stickerMessage': 'webp'
        };
        return typeToExt[messageType] || 'bin';
    }
    getMediaPath(filename) {
        return path.join(this.mediaDir, filename);
    }
    getMediaUrl(filename, baseUrl) {
        return `${baseUrl}/whatsapp/media/${filename}`;
    }
    deleteMedia(filename) {
        try {
            const filepath = path.join(this.mediaDir, filename);
            if (fs.existsSync(filepath)) {
                fs.unlinkSync(filepath);
                return true;
            }
            return false;
        }
        catch (error) {
            console.error('Error deleting media:', error);
            return false;
        }
    }
    getMediaInfo(filename) {
        try {
            const filepath = path.join(this.mediaDir, filename);
            if (fs.existsSync(filepath)) {
                const stats = fs.statSync(filepath);
                return {
                    filename,
                    size: stats.size,
                    created: stats.birthtime,
                    modified: stats.mtime
                };
            }
            return null;
        }
        catch (error) {
            console.error('Error getting media info:', error);
            return null;
        }
    }
}
//# sourceMappingURL=MediaHandler.js.map