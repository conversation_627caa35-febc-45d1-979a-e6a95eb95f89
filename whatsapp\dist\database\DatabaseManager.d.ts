export interface DeviceData {
    id?: number;
    device_name: string;
    device_id: string;
    connection_method: 'qr_code' | 'auth_code';
    status: 'pending' | 'connecting' | 'connected' | 'disconnected' | 'failed';
    phone_number?: string | null;
    display_name?: string | null;
    session_id?: string | null;
    qr_code?: string | null;
    auth_code?: string | null;
    last_connected_at?: Date | null;
    created_at?: Date;
    updated_at?: Date;
}
export interface SessionData {
    session_id: string;
    device_id?: number | null;
    status: string;
    phone_number?: string | null;
    name?: string | null;
    qr_code?: string | null;
    created_at?: Date;
    updated_at?: Date;
}
export interface MessageData {
    message_id: string;
    session_id: string;
    from_jid: string;
    from_me: boolean;
    participant?: string | null;
    message_type: string;
    text_content?: string | null;
    media_url?: string | null;
    media_type?: string | null;
    media_size?: number | null;
    timestamp: Date;
    status: string;
    raw_message?: string;
}
export interface ContactData {
    session_id: string;
    jid: string;
    phone_number?: string | null;
    name?: string | null;
    is_group: boolean;
    last_seen: Date;
}
export declare class DatabaseManager {
    private connection;
    constructor();
    private initializeConnection;
    private createTables;
    storeDevice(deviceData: DeviceData): Promise<number>;
    updateDevice(deviceId: string, updates: Partial<DeviceData>): Promise<void>;
    getDevice(deviceId: string): Promise<DeviceData | null>;
    getAllDevices(): Promise<DeviceData[]>;
    deleteDevice(deviceId: string): Promise<void>;
    storeSession(sessionData: SessionData): Promise<void>;
    updateSession(sessionId: string, updates: Partial<SessionData>): Promise<void>;
    getSession(sessionId: string): Promise<SessionData | null>;
    getAllSessions(): Promise<SessionData[]>;
    storeMessage(messageData: MessageData): Promise<void>;
    updateMessageStatus(messageId: string, status: string): Promise<void>;
    getMessage(messageId: string): Promise<MessageData | null>;
    getMessages(sessionId: string, limit?: number, offset?: number): Promise<MessageData[]>;
    storeContact(contactData: ContactData): Promise<void>;
    getContacts(sessionId: string): Promise<ContactData[]>;
    close(): Promise<void>;
}
//# sourceMappingURL=DatabaseManager.d.ts.map