<?php

namespace Tests\Feature;

// use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ExampleTest extends TestCase
{
    /**
     * A basic test example.
     */
    public function test_the_application_returns_a_successful_response(): void
    {
        $response = $this->get('/');

        $response->assertStatus(302);
        $response->assertRedirect(route('whatsapp.dashboard'));
    }

    /**
     * Test WhatsApp dashboard loads successfully.
     */
    public function test_whatsapp_dashboard_loads(): void
    {
        $response = $this->get(route('whatsapp.dashboard'));

        $response->assertStatus(200);
        $response->assertViewIs('whatsapp.dashboard');
    }
}
