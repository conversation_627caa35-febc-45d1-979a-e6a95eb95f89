#!/usr/bin/env node

/**
 * Simple WhatsApp Session Test
 * 
 * This script tests the WhatsApp gateway directly to create a session
 * and get the QR code.
 */

import axios from 'axios';

const GATEWAY_BASE_URL = 'http://localhost:3000/api';
const sessionId = `test-device-${Date.now()}`;

console.log('📱 Simple WhatsApp Session Test');
console.log('===============================\n');

console.log(`🆔 Session ID: ${sessionId}`);

async function testGatewayDirectly() {
  try {
    console.log('🔄 Testing gateway connection...');
    
    // Test if gateway is running
    const healthResponse = await axios.get(`${GATEWAY_BASE_URL}/sessions`);
    console.log('✅ Gateway is running');
    console.log(`   Response: ${healthResponse.status}`);
    
    // Create a session
    console.log('\n🔄 Creating session...');
    const createResponse = await axios.post(`${GATEWAY_BASE_URL}/sessions`, {
      sessionId: sessionId
    });
    
    console.log('✅ Session creation request sent');
    console.log(`   Response: ${createResponse.status}`);
    console.log(`   Data:`, createResponse.data);
    
    // Wait a bit for QR code generation
    console.log('\n⏳ Waiting for QR code generation...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Check session status
    console.log('\n🔍 Checking session status...');
    const statusResponse = await axios.get(`${GATEWAY_BASE_URL}/sessions/${sessionId}`);
    console.log('✅ Session status retrieved');
    console.log(`   Status:`, statusResponse.data);
    
    if (statusResponse.data.session && statusResponse.data.session.qr) {
      console.log('\n📱 QR CODE AVAILABLE!');
      console.log('====================');
      console.log('🔗 QR Code Data:', statusResponse.data.session.qr.substring(0, 100) + '...');
      console.log('\n📋 Instructions:');
      console.log('1. Open WhatsApp on your phone');
      console.log('2. Go to Settings > Linked Devices');
      console.log('3. Tap "Link a Device"');
      console.log('4. Scan the QR code');
      console.log('\n🌐 You can view the QR code in your browser at:');
      console.log(`   http://localhost:8000/whatsapp/sessions/${sessionId}`);
    } else {
      console.log('\n⏳ QR code not ready yet. Status:', statusResponse.data.session?.status);
      console.log('💡 Try checking again in a few seconds');
    }
    
  } catch (error) {
    console.log('❌ Error:', error.message);
    if (error.response) {
      console.log(`   Response: ${error.response.status}`);
      console.log(`   Data:`, error.response.data);
    }
  }
}

testGatewayDirectly();
