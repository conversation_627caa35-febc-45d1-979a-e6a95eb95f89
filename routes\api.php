<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\WhatsAppController;
use App\Http\Controllers\WhatsAppDeviceController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

/*
|--------------------------------------------------------------------------
| WhatsApp Gateway API Routes
|--------------------------------------------------------------------------
*/

Route::prefix('whatsapp')->group(function () {
    // Device management API
    Route::prefix('devices')->group(function () {
        Route::get('/', [WhatsAppDeviceController::class, 'index']);
        Route::post('/', [WhatsAppDeviceController::class, 'store']);
        Route::get('/{deviceId}', [WhatsAppDeviceController::class, 'show']);
        Route::get('/{deviceId}/status', [WhatsAppDeviceController::class, 'getStatus']);
        Route::patch('/{deviceId}/status', [WhatsAppDeviceController::class, 'updateStatus']);
        Route::post('/{deviceId}/start-connection', [WhatsAppDeviceController::class, 'startConnection']);
        Route::delete('/{deviceId}', [WhatsAppDeviceController::class, 'destroy']);
    });

    // Session management
    Route::post('/sessions', [WhatsAppController::class, 'createSession']);
    Route::get('/sessions', [WhatsAppController::class, 'getSessions']);
    Route::get('/sessions/{sessionId}', [WhatsAppController::class, 'getSession']);
    Route::delete('/sessions/{sessionId}', [WhatsAppController::class, 'deleteSession']);

    // Message sending
    Route::post('/sessions/{sessionId}/send-text', [WhatsAppController::class, 'sendText']);
    Route::post('/sessions/{sessionId}/send-media', [WhatsAppController::class, 'sendMedia']);
    Route::post('/sessions/{sessionId}/send-image', [WhatsAppController::class, 'sendImage']);
    
    // Data retrieval
    Route::get('/sessions/{sessionId}/messages', [WhatsAppController::class, 'getMessages']);
    Route::get('/sessions/{sessionId}/contacts', [WhatsAppController::class, 'getContacts']);
});
