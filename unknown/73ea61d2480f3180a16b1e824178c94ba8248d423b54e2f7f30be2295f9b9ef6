#!/usr/bin/env node

/**
 * WhatsApp Gateway Application Test Runner
 * 
 * This script tests the application setup and configuration
 * without requiring full npm installation.
 */

import fs from 'fs';
import path from 'path';

console.log('🚀 WhatsApp Gateway Application Test Runner');
console.log('============================================\n');

const tests = {
    fileStructure: false,
    configuration: false,
    dependencies: false,
    database: false,
    laravel: false
};

// Test 1: File Structure
console.log('📁 Testing File Structure...');
try {
    const requiredFiles = [
        'package.json',
        'whatsapp/server.ts',
        'whatsapp/gateway/WhatsAppGateway.ts',
        'whatsapp/database/DatabaseManager.ts',
        'whatsapp/routes/api.ts',
        'whatsapp/tsconfig.json',
        'app/Http/Controllers/WhatsAppController.php',
        'config/whatsapp.php',
        '.env'
    ];

    const requiredDirs = [
        'whatsapp',
        'whatsapp/gateway',
        'whatsapp/database',
        'whatsapp/routes',
        'app/Http/Controllers',
        'config',
        'resources/views/whatsapp'
    ];

    let allFilesExist = true;
    let allDirsExist = true;

    console.log('   Checking required files...');
    requiredFiles.forEach(file => {
        if (fs.existsSync(file)) {
            console.log(`   ✅ ${file}`);
        } else {
            console.log(`   ❌ ${file} - MISSING`);
            allFilesExist = false;
        }
    });

    console.log('   Checking required directories...');
    requiredDirs.forEach(dir => {
        if (fs.existsSync(dir)) {
            console.log(`   ✅ ${dir}/`);
        } else {
            console.log(`   ❌ ${dir}/ - MISSING`);
            allDirsExist = false;
        }
    });

    if (allFilesExist && allDirsExist) {
        tests.fileStructure = true;
        console.log('✅ File structure test PASSED\n');
    } else {
        console.log('❌ File structure test FAILED\n');
    }
} catch (error) {
    console.log(`❌ File structure test ERROR: ${error.message}\n`);
}

// Test 2: Configuration Files
console.log('⚙️  Testing Configuration...');
try {
    // Check package.json
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const hasRequiredScripts = packageJson.scripts && 
        packageJson.scripts['whatsapp:dev'] && 
        packageJson.scripts['whatsapp:verify'];
    
    const hasRequiredDeps = packageJson.dependencies && 
        packageJson.dependencies['baileys'] && 
        packageJson.dependencies['express'] && 
        packageJson.dependencies['socket.io'];

    if (hasRequiredScripts && hasRequiredDeps) {
        console.log('   ✅ package.json configuration is correct');
    } else {
        console.log('   ❌ package.json missing required scripts or dependencies');
    }

    // Check .env file
    const envContent = fs.readFileSync('.env', 'utf8');
    const hasWhatsAppConfig = envContent.includes('WHATSAPP_GATEWAY_URL') && 
        envContent.includes('WHATSAPP_GATEWAY_PORT');

    if (hasWhatsAppConfig) {
        console.log('   ✅ .env WhatsApp configuration found');
        tests.configuration = true;
        console.log('✅ Configuration test PASSED\n');
    } else {
        console.log('   ❌ .env missing WhatsApp configuration');
        console.log('❌ Configuration test FAILED\n');
    }
} catch (error) {
    console.log(`❌ Configuration test ERROR: ${error.message}\n`);
}

// Test 3: Dependencies Check
console.log('📦 Testing Dependencies...');
try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const requiredDeps = [
        'baileys',
        '@hapi/boom',
        'express',
        'cors',
        'socket.io',
        'mysql2',
        'dotenv',
        'node-cache',
        'pino'
    ];

    let allDepsPresent = true;
    requiredDeps.forEach(dep => {
        if (packageJson.dependencies[dep]) {
            console.log(`   ✅ ${dep}: ${packageJson.dependencies[dep]}`);
        } else {
            console.log(`   ❌ ${dep} - MISSING`);
            allDepsPresent = false;
        }
    });

    if (allDepsPresent) {
        tests.dependencies = true;
        console.log('✅ Dependencies test PASSED\n');
    } else {
        console.log('❌ Dependencies test FAILED\n');
    }
} catch (error) {
    console.log(`❌ Dependencies test ERROR: ${error.message}\n`);
}

// Test 4: Database Configuration
console.log('🗄️  Testing Database Configuration...');
try {
    const envContent = fs.readFileSync('.env', 'utf8');
    const dbConfig = {
        host: envContent.match(/DB_HOST=(.+)/)?.[1],
        port: envContent.match(/DB_PORT=(.+)/)?.[1],
        database: envContent.match(/DB_DATABASE=(.+)/)?.[1],
        username: envContent.match(/DB_USERNAME=(.+)/)?.[1]
    };

    if (dbConfig.host && dbConfig.port && dbConfig.database && dbConfig.username) {
        console.log(`   ✅ Database Host: ${dbConfig.host}`);
        console.log(`   ✅ Database Port: ${dbConfig.port}`);
        console.log(`   ✅ Database Name: ${dbConfig.database}`);
        console.log(`   ✅ Database User: ${dbConfig.username}`);
        tests.database = true;
        console.log('✅ Database configuration test PASSED\n');
    } else {
        console.log('   ❌ Database configuration incomplete');
        console.log('❌ Database configuration test FAILED\n');
    }
} catch (error) {
    console.log(`❌ Database configuration test ERROR: ${error.message}\n`);
}

// Test 5: Laravel Integration
console.log('🎯 Testing Laravel Integration...');
try {
    const controllerExists = fs.existsSync('app/Http/Controllers/WhatsAppController.php');
    const configExists = fs.existsSync('config/whatsapp.php');
    const routesExist = fs.readFileSync('routes/api.php', 'utf8').includes('whatsapp');
    const viewsExist = fs.existsSync('resources/views/whatsapp/dashboard.blade.php');

    if (controllerExists && configExists && routesExist && viewsExist) {
        console.log('   ✅ WhatsApp Controller exists');
        console.log('   ✅ WhatsApp config file exists');
        console.log('   ✅ API routes configured');
        console.log('   ✅ Dashboard views exist');
        tests.laravel = true;
        console.log('✅ Laravel integration test PASSED\n');
    } else {
        console.log('   ❌ Laravel integration incomplete');
        if (!controllerExists) console.log('   ❌ WhatsApp Controller missing');
        if (!configExists) console.log('   ❌ WhatsApp config missing');
        if (!routesExist) console.log('   ❌ API routes not configured');
        if (!viewsExist) console.log('   ❌ Dashboard views missing');
        console.log('❌ Laravel integration test FAILED\n');
    }
} catch (error) {
    console.log(`❌ Laravel integration test ERROR: ${error.message}\n`);
}

// Summary
console.log('📊 TEST SUMMARY');
console.log('===============');

const totalTests = Object.keys(tests).length;
const passedTests = Object.values(tests).filter(Boolean).length;
const percentage = Math.round((passedTests / totalTests) * 100);

Object.entries(tests).forEach(([test, passed]) => {
    const status = passed ? '✅' : '❌';
    const result = passed ? 'PASS' : 'FAIL';
    console.log(`${status} ${test}: ${result}`);
});

console.log(`\n🎯 Overall Score: ${passedTests}/${totalTests} (${percentage}%)`);

if (percentage === 100) {
    console.log('🎉 ALL TESTS PASSED! Your application is ready to run.');
    console.log('\n📋 Next Steps:');
    console.log('1. Run: npm install');
    console.log('2. Run: npm run whatsapp:verify');
    console.log('3. Run: npm run whatsapp:dev');
    console.log('4. Run: php artisan serve');
    console.log('5. Open: http://localhost:8000');
} else if (percentage >= 80) {
    console.log('✅ MOST TESTS PASSED! Minor issues need to be resolved.');
} else {
    console.log('⚠️  SEVERAL TESTS FAILED! Please review the setup.');
}

console.log('\n🔧 Installation Commands:');
console.log('npm install                    # Install dependencies');
console.log('npm run whatsapp:verify       # Verify Baileys compliance');
console.log('npm run whatsapp:dev          # Start WhatsApp gateway');
console.log('php artisan serve             # Start Laravel server');

process.exit(percentage === 100 ? 0 : 1);