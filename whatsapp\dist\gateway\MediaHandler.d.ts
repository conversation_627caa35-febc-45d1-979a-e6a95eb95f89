import { WAMessage } from 'baileys';
export declare class MediaHandler {
    private mediaDir;
    constructor();
    private ensureMediaDirectory;
    downloadAndSaveMedia(message: WAMessage): Promise<string | null>;
    private getFileExtension;
    getMediaPath(filename: string): string;
    getMediaUrl(filename: string, baseUrl: string): string;
    deleteMedia(filename: string): boolean;
    getMediaInfo(filename: string): any;
}
//# sourceMappingURL=MediaHandler.d.ts.map