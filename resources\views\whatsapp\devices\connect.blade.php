<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connect Device - {{ $device->device_name }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <meta name="csrf-token" content="{{ csrf_token() }}">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen">
        <!-- Navigation -->
        <nav class="bg-green-600 text-white p-4">
            <div class="container mx-auto flex justify-between items-center">
                <h1 class="text-2xl font-bold">
                    <i class="fab fa-whatsapp mr-2"></i>
                    Connect Device: {{ $device->device_name }}
                </h1>
                <div class="space-x-4">
                    <a href="{{ route('whatsapp.dashboard') }}" class="hover:text-green-200">Dashboard</a>
                    <a href="{{ route('whatsapp.devices.index') }}" class="hover:text-green-200">Device Management</a>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="container mx-auto p-6">
            <div class="max-w-4xl mx-auto">
                <!-- Device Info Header -->
                <div class="bg-white rounded-lg shadow p-6 mb-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h2 class="text-2xl font-bold text-gray-800">{{ $device->device_name }}</h2>
                            <p class="text-gray-600">Device ID: <span class="font-mono">{{ $device->device_id }}</span></p>
                            <p class="text-gray-600">Connection Method: <span class="font-semibold">{{ $device->connection_method_display }}</span></p>
                        </div>
                        <div class="text-right">
                            <span class="px-3 py-1 rounded-full text-sm {{ $device->status_badge_class }}">
                                {{ $device->status_display }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Connection Status -->
                <div class="bg-white rounded-lg shadow p-6 mb-6">
                    <h3 class="text-lg font-semibold mb-4">Connection Status</h3>
                    <div id="connection-status" class="flex items-center">
                        <div class="w-3 h-3 bg-yellow-500 rounded-full mr-3 animate-pulse"></div>
                        <span class="text-yellow-600">Initializing connection...</span>
                    </div>
                    
                    <!-- Status Messages -->
                    <div id="status-messages" class="mt-4 space-y-2 max-h-32 overflow-y-auto">
                        <!-- Status messages will be added here -->
                    </div>
                </div>

                @if($device->connection_method === 'qr_code')
                    <!-- QR Code Section -->
                    <div class="bg-white rounded-lg shadow p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4">
                            <i class="fas fa-qrcode mr-2"></i>QR Code Scanner
                        </h3>
                        
                        <div class="grid md:grid-cols-2 gap-6">
                            <!-- QR Code Display -->
                            <div class="text-center">
                                <div id="qr-container" class="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-8 mb-4">
                                    <div id="qr-loading" class="flex flex-col items-center">
                                        <i class="fas fa-spinner fa-spin text-4xl text-gray-400 mb-4"></i>
                                        <p class="text-gray-600">Generating QR Code...</p>
                                    </div>
                                    <div id="qr-display" class="hidden">
                                        <img id="qr-image" src="" alt="WhatsApp QR Code" class="mx-auto max-w-full h-auto">
                                    </div>
                                    <div id="qr-error" class="hidden text-center">
                                        <i class="fas fa-exclamation-triangle text-4xl text-red-400 mb-4"></i>
                                        <p class="text-red-600 mb-4">Failed to generate QR code</p>
                                        <button onclick="refreshQR()" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">
                                            Try Again
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="flex justify-center space-x-4">
                                    <button onclick="refreshQR()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                                        <i class="fas fa-sync mr-2"></i>Refresh QR
                                    </button>
                                    <button id="download-btn" onclick="downloadQR()" disabled class="bg-gray-400 text-white px-4 py-2 rounded cursor-not-allowed">
                                        <i class="fas fa-download mr-2"></i>Download
                                    </button>
                                </div>
                                
                                <!-- Auto-refresh timer -->
                                <div class="mt-4 text-sm text-gray-500">
                                    <p>QR Code auto-refreshes every <span id="refresh-timer">10</span> seconds</p>
                                    <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
                                        <div id="refresh-progress" class="bg-green-600 h-2 rounded-full transition-all duration-1000" style="width: 0%"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Instructions -->
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-3">How to connect:</h4>
                                <ol class="space-y-3 text-sm text-gray-600">
                                    <li class="flex items-start">
                                        <span class="bg-green-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mr-3 mt-0.5">1</span>
                                        <span>Open WhatsApp on your phone</span>
                                    </li>
                                    <li class="flex items-start">
                                        <span class="bg-green-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mr-3 mt-0.5">2</span>
                                        <span>Go to <strong>Settings</strong> → <strong>Linked Devices</strong></span>
                                    </li>
                                    <li class="flex items-start">
                                        <span class="bg-green-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mr-3 mt-0.5">3</span>
                                        <span>Tap <strong>"Link a Device"</strong></span>
                                    </li>
                                    <li class="flex items-start">
                                        <span class="bg-green-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mr-3 mt-0.5">4</span>
                                        <span>Scan the QR code displayed on the left</span>
                                    </li>
                                </ol>
                                
                                <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                    <div class="flex">
                                        <i class="fas fa-info-circle text-blue-600 mt-1 mr-3"></i>
                                        <div>
                                            <h5 class="font-medium text-blue-900 mb-1">Important Notes:</h5>
                                            <ul class="text-sm text-blue-800 space-y-1">
                                                <li>• Keep this page open during the connection process</li>
                                                <li>• QR code refreshes automatically every 10 seconds</li>
                                                <li>• Connection usually takes 5-15 seconds after scanning</li>
                                                <li>• Make sure your phone has internet connection</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @else
                    <!-- Auth Code Section -->
                    <div class="bg-white rounded-lg shadow p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4">
                            <i class="fas fa-key mr-2"></i>Authentication Code
                        </h3>
                        
                        <div class="text-center">
                            <p class="text-gray-600 mb-4">Authentication code method will be implemented in the next phase.</p>
                            <p class="text-sm text-gray-500">For now, please use QR Code method by creating a new device.</p>
                            <a href="{{ route('whatsapp.devices.create') }}" class="inline-block mt-4 bg-green-600 text-white px-6 py-2 rounded hover:bg-green-700">
                                Create QR Code Device
                            </a>
                        </div>
                    </div>
                @endif

                <!-- Action Buttons -->
                <div class="flex justify-between items-center">
                    <a href="{{ route('whatsapp.devices.index') }}" class="text-gray-600 hover:text-gray-800">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Devices
                    </a>
                    
                    <div class="space-x-4">
                        <button onclick="cancelConnection()" class="bg-gray-600 text-white px-6 py-2 rounded hover:bg-gray-700">
                            Cancel Connection
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Success Modal -->
    <div id="successModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg p-8 max-w-md w-full text-center">
                <div class="mb-4">
                    <i class="fas fa-check-circle text-6xl text-green-600"></i>
                </div>
                <h3 class="text-2xl font-bold text-gray-800 mb-2">Device Connected!</h3>
                <p class="text-gray-600 mb-6">Your WhatsApp device has been successfully connected and is ready to use.</p>
                <div class="space-y-3">
                    <button onclick="goToDeviceDetails()" class="w-full bg-green-600 text-white py-3 rounded-lg hover:bg-green-700">
                        View Device Details
                    </button>
                    <button onclick="goToDashboard()" class="w-full bg-gray-600 text-white py-3 rounded-lg hover:bg-gray-700">
                        Go to Dashboard
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Configuration
        const deviceId = '{{ $device->device_id }}';
        const connectionMethod = '{{ $device->connection_method }}';
        const socketUrl = '{{ config("whatsapp.socket_url", "http://localhost:3000") }}';
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        // State management
        let sessionId = null;
        let currentStatus = 'initializing';
        let qrCodeData = null;
        let refreshTimer = null;
        let refreshInterval = 10; // seconds
        let progressInterval = null;
        
        // Socket.IO connection
        const socket = io(socketUrl);
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            if (connectionMethod === 'qr_code') {
                startConnection();
            }
        });
        
        // Socket event handlers
        socket.on('connect', () => {
            updateConnectionStatus('connected', 'Connected to WhatsApp Gateway');
            addStatusMessage('Connected to WhatsApp Gateway', 'success');
        });
        
        socket.on('disconnect', () => {
            updateConnectionStatus('disconnected', 'Disconnected from Gateway');
            addStatusMessage('Disconnected from Gateway', 'error');
        });
        
        socket.on('qr-updated', (data) => {
            if (data.sessionId === sessionId) {
                displayQRCode(data.qr);
                addStatusMessage('QR Code generated - ready to scan', 'info');
                startRefreshTimer();
            }
        });
        
        socket.on('session-status', (data) => {
            if (data.sessionId === sessionId) {
                handleSessionStatus(data.status, data.info);
            }
        });
        
        socket.on('session-error', (data) => {
            if (data.sessionId === sessionId) {
                showQRError();
                addStatusMessage('Error: ' + data.error, 'error');
            }
        });
        
        // Start connection process
        async function startConnection() {
            try {
                updateConnectionStatus('connecting', 'Starting connection...');
                addStatusMessage('Initializing device connection...', 'info');
                
                const response = await fetch(`/whatsapp/devices/${deviceId}/start-connection`, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': csrfToken,
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    sessionId = result.session_id;
                    addStatusMessage('Session created successfully', 'success');
                    addStatusMessage('Generating QR code...', 'info');
                } else {
                    throw new Error(result.message || 'Failed to start connection');
                }
            } catch (error) {
                updateConnectionStatus('error', 'Connection failed');
                addStatusMessage('Failed to start connection: ' + error.message, 'error');
            }
        }
        
        // Handle session status updates
        function handleSessionStatus(status, info) {
            currentStatus = status;
            
            if (status === 'connected') {
                clearRefreshTimer();
                updateConnectionStatus('connected', 'Device Connected Successfully!');
                addStatusMessage('Device connected successfully!', 'success');
                
                // Update device status via API
                updateDeviceStatus('connected', info);
                
                setTimeout(() => {
                    document.getElementById('successModal').classList.remove('hidden');
                }, 1000);
            } else if (status === 'qr') {
                updateConnectionStatus('qr', 'Waiting for QR Scan');
                addStatusMessage('QR code ready - waiting for scan', 'info');
            } else if (status === 'connecting') {
                updateConnectionStatus('connecting', 'Connecting...');
                addStatusMessage('Connecting to WhatsApp...', 'info');
            } else if (status === 'disconnected') {
                updateConnectionStatus('disconnected', 'Disconnected');
                addStatusMessage('Device disconnected', 'error');
            }
        }
        
        // Display QR code
        function displayQRCode(qrData) {
            qrCodeData = qrData;
            
            document.getElementById('qr-loading').classList.add('hidden');
            document.getElementById('qr-error').classList.add('hidden');
            
            const qrImage = document.getElementById('qr-image');
            qrImage.src = qrData;
            document.getElementById('qr-display').classList.remove('hidden');
            document.getElementById('download-btn').disabled = false;
            document.getElementById('download-btn').className = 'bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700';
        }
        
        // Show QR error
        function showQRError() {
            document.getElementById('qr-loading').classList.add('hidden');
            document.getElementById('qr-display').classList.add('hidden');
            document.getElementById('qr-error').classList.remove('hidden');
            document.getElementById('download-btn').disabled = true;
            document.getElementById('download-btn').className = 'bg-gray-400 text-white px-4 py-2 rounded cursor-not-allowed';
        }
        
        // Refresh QR code
        function refreshQR() {
            document.getElementById('qr-display').classList.add('hidden');
            document.getElementById('qr-error').classList.add('hidden');
            document.getElementById('qr-loading').classList.remove('hidden');
            document.getElementById('download-btn').disabled = true;
            document.getElementById('download-btn').className = 'bg-gray-400 text-white px-4 py-2 rounded cursor-not-allowed';
            
            addStatusMessage('Refreshing QR code...', 'info');
            
            // Request new QR code
            if (sessionId) {
                socket.emit('refresh-qr', { sessionId });
            } else {
                startConnection();
            }
        }
        
        // Start refresh timer
        function startRefreshTimer() {
            clearRefreshTimer();
            
            let timeLeft = refreshInterval;
            const timerElement = document.getElementById('refresh-timer');
            const progressElement = document.getElementById('refresh-progress');
            
            refreshTimer = setInterval(() => {
                timeLeft--;
                timerElement.textContent = timeLeft;
                
                const progress = ((refreshInterval - timeLeft) / refreshInterval) * 100;
                progressElement.style.width = progress + '%';
                
                if (timeLeft <= 0) {
                    refreshQR();
                }
            }, 1000);
            
            // Update progress bar smoothly
            progressInterval = setInterval(() => {
                const currentProgress = parseFloat(progressElement.style.width) || 0;
                if (currentProgress < 100) {
                    progressElement.style.width = (currentProgress + (100 / (refreshInterval * 10))) + '%';
                }
            }, 100);
        }
        
        // Clear refresh timer
        function clearRefreshTimer() {
            if (refreshTimer) {
                clearInterval(refreshTimer);
                refreshTimer = null;
            }
            if (progressInterval) {
                clearInterval(progressInterval);
                progressInterval = null;
            }
            
            document.getElementById('refresh-timer').textContent = refreshInterval;
            document.getElementById('refresh-progress').style.width = '0%';
        }
        
        // Update connection status
        function updateConnectionStatus(status, message) {
            const statusElement = document.getElementById('connection-status');
            const statusClasses = {
                'connecting': 'text-blue-600',
                'connected': 'text-green-600',
                'disconnected': 'text-red-600',
                'error': 'text-red-600',
                'qr': 'text-yellow-600'
            };
            
            const dotClasses = {
                'connecting': 'bg-blue-500 animate-pulse',
                'connected': 'bg-green-500',
                'disconnected': 'bg-red-500',
                'error': 'bg-red-500',
                'qr': 'bg-yellow-500 animate-pulse'
            };
            
            statusElement.innerHTML = `
                <div class="w-3 h-3 ${dotClasses[status] || 'bg-gray-500'} rounded-full mr-3"></div>
                <span class="${statusClasses[status] || 'text-gray-600'}">${message}</span>
            `;
        }
        
        // Add status message
        function addStatusMessage(message, type) {
            const messagesContainer = document.getElementById('status-messages');
            const messageElement = document.createElement('div');
            
            const typeClasses = {
                'success': 'text-green-600',
                'error': 'text-red-600',
                'info': 'text-blue-600',
                'warning': 'text-yellow-600'
            };
            
            const typeIcons = {
                'success': 'fa-check-circle',
                'error': 'fa-exclamation-circle',
                'info': 'fa-info-circle',
                'warning': 'fa-exclamation-triangle'
            };
            
            messageElement.className = `text-sm ${typeClasses[type] || 'text-gray-600'}`;
            messageElement.innerHTML = `
                <i class="fas ${typeIcons[type] || 'fa-circle'} mr-2"></i>
                <span class="text-gray-500">${new Date().toLocaleTimeString()}</span> - ${message}
            `;
            
            messagesContainer.appendChild(messageElement);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        // Update device status
        async function updateDeviceStatus(status, info) {
            try {
                const updateData = { status };
                
                if (info) {
                    if (info.id) updateData.phone_number = info.id;
                    if (info.name) updateData.display_name = info.name;
                }
                
                await fetch(`/api/whatsapp/devices/${deviceId}/status`, {
                    method: 'PATCH',
                    headers: {
                        'X-CSRF-TOKEN': csrfToken,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(updateData)
                });
            } catch (error) {
                console.error('Failed to update device status:', error);
            }
        }
        
        // Download QR code
        function downloadQR() {
            if (qrCodeData) {
                const link = document.createElement('a');
                link.href = qrCodeData;
                link.download = `whatsapp-qr-${deviceId}.png`;
                link.click();
            }
        }
        
        // Cancel connection
        function cancelConnection() {
            if (confirm('Are you sure you want to cancel the connection process?')) {
                clearRefreshTimer();
                window.location.href = '{{ route("whatsapp.devices.index") }}';
            }
        }
        
        // Modal actions
        function goToDeviceDetails() {
            window.location.href = `/whatsapp/devices/${deviceId}`;
        }
        
        function goToDashboard() {
            window.location.href = '{{ route("whatsapp.dashboard") }}';
        }
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            clearRefreshTimer();
        });
    </script>
</body>
</html>
