#!/usr/bin/env node

/**
 * Test UI Functionality
 * 
 * This script tests the new UI/UX functionality for QR code scanning
 */

import axios from 'axios';

const LARAVEL_BASE_URL = 'http://127.0.0.1:8000';
const GATEWAY_BASE_URL = 'http://localhost:3000';

console.log('🧪 Testing WhatsApp UI/UX Functionality');
console.log('======================================\n');

async function testUIEndpoints() {
    const tests = {
        dashboard: false,
        sessions: false,
        connectDevice: false,
        gatewayAPI: false,
        sessionCreation: false
    };

    console.log('1. Testing Dashboard Access...');
    try {
        const response = await axios.get(`${LARAVEL_BASE_URL}/whatsapp/dashboard`);
        if (response.status === 200) {
            tests.dashboard = true;
            console.log('✅ Dashboard accessible');
        }
    } catch (error) {
        console.log('❌ Dashboard failed:', error.message);
    }

    console.log('\n2. Testing Sessions Page...');
    try {
        const response = await axios.get(`${LARAVEL_BASE_URL}/whatsapp/sessions`);
        if (response.status === 200) {
            tests.sessions = true;
            console.log('✅ Sessions page accessible');
        }
    } catch (error) {
        console.log('❌ Sessions page failed:', error.message);
    }

    console.log('\n3. Testing Connect Device Page...');
    try {
        const response = await axios.get(`${LARAVEL_BASE_URL}/whatsapp/connect-device`);
        if (response.status === 200) {
            tests.connectDevice = true;
            console.log('✅ Connect Device page accessible');
        }
    } catch (error) {
        console.log('❌ Connect Device page failed:', error.message);
    }

    console.log('\n4. Testing Gateway API...');
    try {
        const response = await axios.get(`${GATEWAY_BASE_URL}/api/sessions`);
        if (response.status === 200) {
            tests.gatewayAPI = true;
            console.log('✅ Gateway API accessible');
            console.log(`   Found ${response.data.sessions?.length || 0} sessions`);
        }
    } catch (error) {
        console.log('❌ Gateway API failed:', error.message);
    }

    console.log('\n5. Testing Session Creation via UI...');
    try {
        const sessionId = `ui-test-${Date.now()}`;
        const response = await axios.post(`${LARAVEL_BASE_URL}/api/whatsapp/sessions`, {
            session_id: sessionId
        }, {
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        if (response.status === 200 || response.status === 201) {
            tests.sessionCreation = true;
            console.log('✅ Session creation via UI successful');
            console.log(`   Session ID: ${sessionId}`);
            
            // Test session detail page
            try {
                const detailResponse = await axios.get(`${LARAVEL_BASE_URL}/whatsapp/sessions/${sessionId}`);
                if (detailResponse.status === 200) {
                    console.log('✅ Session detail page accessible');
                }
            } catch (detailError) {
                console.log('⚠️  Session detail page not accessible yet');
            }
        }
    } catch (error) {
        console.log('❌ Session creation failed:', error.message);
        if (error.response) {
            console.log(`   Response: ${error.response.status} - ${JSON.stringify(error.response.data).substring(0, 100)}...`);
        }
    }

    // Summary
    console.log('\n📊 UI/UX TEST SUMMARY');
    console.log('=====================');
    
    const totalTests = Object.keys(tests).length;
    const passedTests = Object.values(tests).filter(Boolean).length;
    const percentage = Math.round((passedTests / totalTests) * 100);
    
    Object.entries(tests).forEach(([test, passed]) => {
        const status = passed ? '✅' : '❌';
        const result = passed ? 'PASS' : 'FAIL';
        console.log(`${status} ${test}: ${result}`);
    });
    
    console.log(`\n🎯 Overall Score: ${passedTests}/${totalTests} (${percentage}%)`);
    
    if (percentage === 100) {
        console.log('🎉 ALL UI TESTS PASSED! The interface is working perfectly.');
    } else if (percentage >= 80) {
        console.log('✅ MOST UI TESTS PASSED! Interface is mostly functional.');
    } else {
        console.log('⚠️  SEVERAL UI TESTS FAILED! Please check the setup.');
    }
    
    console.log('\n🌐 Available UI Pages:');
    console.log(`📊 Dashboard: ${LARAVEL_BASE_URL}/whatsapp/dashboard`);
    console.log(`📱 Sessions: ${LARAVEL_BASE_URL}/whatsapp/sessions`);
    console.log(`📱 Quick Connect: ${LARAVEL_BASE_URL}/whatsapp/connect-device`);
    
    console.log('\n🎨 UI Features:');
    console.log('✨ Modern responsive design with Tailwind CSS');
    console.log('📱 Real-time QR code generation and display');
    console.log('🔄 Live status updates via Socket.IO');
    console.log('📋 Step-by-step connection instructions');
    console.log('🎯 Progress indicators and visual feedback');
    console.log('📊 Session management dashboard');
    console.log('🔗 Quick action buttons and navigation');
    
    return percentage;
}

// Run tests
testUIEndpoints()
    .then(score => {
        process.exit(score >= 80 ? 0 : 1);
    })
    .catch(error => {
        console.error('Test execution failed:', error);
        process.exit(1);
    });
