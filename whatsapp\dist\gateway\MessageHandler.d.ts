import { WAMessage } from 'baileys';
import { DatabaseManager } from '../database/DatabaseManager';
export declare class MessageHandler {
    private dbManager;
    constructor(dbManager: DatabaseManager);
    processIncomingMessage(sessionId: string, message: WAMessage): Promise<void>;
    storeSentMessage(sessionId: string, to: string, messageData: any, sentMessage: any): Promise<void>;
    handleMessageUpdate(sessionId: string, update: any): Promise<void>;
    private extractTextContent;
    private isMediaMessage;
    private getMediaType;
    private getMediaSize;
    private extractAndStoreContact;
}
//# sourceMappingURL=MessageHandler.d.ts.map