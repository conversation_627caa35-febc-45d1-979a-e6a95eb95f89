import multer from 'multer';
import path from 'path';
// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        cb(null, path.join(process.cwd(), 'whatsapp', 'uploads'));
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
    }
});
const upload = multer({
    storage,
    limits: {
        fileSize: 50 * 1024 * 1024 // 50MB limit
    }
});
export function setupRoutes(app, gateway) {
    // Health check
    app.get('/health', (req, res) => {
        res.json({ status: 'ok', timestamp: new Date().toISOString() });
    });
    // Session management
    app.post('/api/sessions', async (req, res) => {
        try {
            const { sessionId } = req.body;
            if (!sessionId) {
                return res.status(400).json({ error: 'Session ID is required' });
            }
            await gateway.createSession(sessionId);
            res.json({ success: true, sessionId });
        }
        catch (error) {
            res.status(500).json({ error: error.message });
        }
    });
    app.get('/api/sessions', async (req, res) => {
        try {
            const sessions = await gateway.getAllSessions();
            res.json({ sessions });
        }
        catch (error) {
            res.status(500).json({ error: error.message });
        }
    });
    app.get('/api/sessions/:sessionId', async (req, res) => {
        try {
            const { sessionId } = req.params;
            const session = await gateway.getSessionStatus(sessionId);
            if (!session) {
                return res.status(404).json({ error: 'Session not found' });
            }
            res.json({ session });
        }
        catch (error) {
            res.status(500).json({ error: error.message });
        }
    });
    app.delete('/api/sessions/:sessionId', async (req, res) => {
        try {
            const { sessionId } = req.params;
            await gateway.deleteSession(sessionId);
            res.json({ success: true, message: 'Session deleted' });
        }
        catch (error) {
            res.status(500).json({ error: error.message });
        }
    });
    // Message sending
    app.post('/api/sessions/:sessionId/send-message', async (req, res) => {
        try {
            const { sessionId } = req.params;
            const { to, message } = req.body;
            if (!to || !message) {
                return res.status(400).json({ error: 'Recipient and message are required' });
            }
            const result = await gateway.sendMessage(sessionId, to, message);
            res.json(result);
        }
        catch (error) {
            res.status(500).json({ error: error.message });
        }
    });
    // Send text message
    app.post('/api/sessions/:sessionId/send-text', async (req, res) => {
        try {
            const { sessionId } = req.params;
            const { to, text } = req.body;
            if (!to || !text) {
                return res.status(400).json({ error: 'Recipient and text are required' });
            }
            const result = await gateway.sendMessage(sessionId, to, { type: 'text', text });
            res.json(result);
        }
        catch (error) {
            res.status(500).json({ error: error.message });
        }
    });
    // Send media message
    app.post('/api/sessions/:sessionId/send-media', upload.single('media'), async (req, res) => {
        try {
            const { sessionId } = req.params;
            const { to, caption, type } = req.body;
            const file = req.file;
            if (!to || !file) {
                return res.status(400).json({ error: 'Recipient and media file are required' });
            }
            const mediaMessage = {
                type: type || 'document',
                url: file.path,
                caption: caption || '',
                fileName: file.originalname,
                mimetype: file.mimetype
            };
            const result = await gateway.sendMessage(sessionId, to, mediaMessage);
            res.json(result);
        }
        catch (error) {
            res.status(500).json({ error: error.message });
        }
    });
    // Send image
    app.post('/api/sessions/:sessionId/send-image', upload.single('image'), async (req, res) => {
        try {
            const { sessionId } = req.params;
            const { to, caption } = req.body;
            const file = req.file;
            if (!to || !file) {
                return res.status(400).json({ error: 'Recipient and image file are required' });
            }
            const result = await gateway.sendMessage(sessionId, to, {
                type: 'image',
                url: file.path,
                caption: caption || ''
            });
            res.json(result);
        }
        catch (error) {
            res.status(500).json({ error: error.message });
        }
    });
    // Send video
    app.post('/api/sessions/:sessionId/send-video', upload.single('video'), async (req, res) => {
        try {
            const { sessionId } = req.params;
            const { to, caption } = req.body;
            const file = req.file;
            if (!to || !file) {
                return res.status(400).json({ error: 'Recipient and video file are required' });
            }
            const result = await gateway.sendMessage(sessionId, to, {
                type: 'video',
                url: file.path,
                caption: caption || ''
            });
            res.json(result);
        }
        catch (error) {
            res.status(500).json({ error: error.message });
        }
    });
    // Send audio
    app.post('/api/sessions/:sessionId/send-audio', upload.single('audio'), async (req, res) => {
        try {
            const { sessionId } = req.params;
            const { to } = req.body;
            const file = req.file;
            if (!to || !file) {
                return res.status(400).json({ error: 'Recipient and audio file are required' });
            }
            const result = await gateway.sendMessage(sessionId, to, {
                type: 'audio',
                url: file.path
            });
            res.json(result);
        }
        catch (error) {
            res.status(500).json({ error: error.message });
        }
    });
    // Send document
    app.post('/api/sessions/:sessionId/send-document', upload.single('document'), async (req, res) => {
        try {
            const { sessionId } = req.params;
            const { to } = req.body;
            const file = req.file;
            if (!to || !file) {
                return res.status(400).json({ error: 'Recipient and document file are required' });
            }
            const result = await gateway.sendMessage(sessionId, to, {
                type: 'document',
                url: file.path,
                fileName: file.originalname,
                mimetype: file.mimetype
            });
            res.json(result);
        }
        catch (error) {
            res.status(500).json({ error: error.message });
        }
    });
    // Get messages
    app.get('/api/sessions/:sessionId/messages', async (req, res) => {
        try {
            const { sessionId } = req.params;
            const limit = parseInt(req.query.limit) || 50;
            const offset = parseInt(req.query.offset) || 0;
            // This would need to be implemented in the gateway
            // const messages = await gateway.getMessages(sessionId, limit, offset);
            res.json({ messages: [], sessionId, limit, offset });
        }
        catch (error) {
            res.status(500).json({ error: error.message });
        }
    });
    // Get contacts
    app.get('/api/sessions/:sessionId/contacts', async (req, res) => {
        try {
            const { sessionId } = req.params;
            // This would need to be implemented in the gateway
            // const contacts = await gateway.getContacts(sessionId);
            res.json({ contacts: [], sessionId });
        }
        catch (error) {
            res.status(500).json({ error: error.message });
        }
    });
    // Download media
    app.get('/api/sessions/:sessionId/media/:messageId', async (req, res) => {
        try {
            const { sessionId, messageId } = req.params;
            const mediaBuffer = await gateway.downloadMedia(sessionId, messageId);
            if (!mediaBuffer) {
                return res.status(404).json({ error: 'Media not found' });
            }
            res.setHeader('Content-Type', 'application/octet-stream');
            res.send(mediaBuffer);
        }
        catch (error) {
            res.status(500).json({ error: error.message });
        }
    });
    // Serve media files
    app.use('/whatsapp/media', (req, res, next) => {
        const mediaPath = path.join(process.cwd(), 'whatsapp', 'media');
        req.url = req.url; // Keep original URL
        next();
    });
    // Webhook endpoints (for future implementation)
    app.post('/api/sessions/:sessionId/webhooks', async (req, res) => {
        try {
            const { sessionId } = req.params;
            const { url, events } = req.body;
            if (!url || !events) {
                return res.status(400).json({ error: 'URL and events are required' });
            }
            // Implementation would store webhook configuration
            res.json({ success: true, message: 'Webhook configured' });
        }
        catch (error) {
            res.status(500).json({ error: error.message });
        }
    });
    // Error handling middleware
    app.use((error, req, res, next) => {
        console.error('API Error:', error);
        res.status(500).json({ error: 'Internal server error' });
    });
}
//# sourceMappingURL=api.js.map