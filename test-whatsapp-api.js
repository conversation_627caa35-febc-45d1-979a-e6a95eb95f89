#!/usr/bin/env node

/**
 * WhatsApp API Testing Script
 * 
 * This script tests all the WhatsApp API endpoints to ensure
 * the integration between Laravel and TypeScript gateway is working.
 */

import axios from 'axios';

const LARAVEL_BASE_URL = 'http://127.0.0.1:8000/api/whatsapp';
const GATEWAY_BASE_URL = 'http://localhost:3000/api';

console.log('🧪 WhatsApp API Integration Test');
console.log('================================\n');

const tests = {
  laravelConnection: false,
  gatewayConnection: false,
  sessionCreation: false,
  sessionList: false,
  sessionDetail: false,
  messageEndpoint: false,
  contactEndpoint: false
};

// Test 1: Laravel API Connection
async function testLaravelConnection() {
  console.log('🔍 Testing Laravel API connection...');
  try {
    const response = await axios.get(`${LARAVEL_BASE_URL}/sessions`);
    if (response.status === 200) {
      tests.laravelConnection = true;
      console.log('✅ Laravel API is accessible');
      console.log(`   Response: ${response.status} - ${JSON.stringify(response.data).substring(0, 100)}...`);
    }
  } catch (error) {
    console.log('❌ Laravel API connection failed');
    console.log(`   Error: ${error.message}`);
  }
}

// Test 2: Gateway Direct Connection
async function testGatewayConnection() {
  console.log('🔍 Testing WhatsApp Gateway direct connection...');
  try {
    const response = await axios.get(`${GATEWAY_BASE_URL}/sessions`);
    if (response.status === 200) {
      tests.gatewayConnection = true;
      console.log('✅ WhatsApp Gateway is accessible');
      console.log(`   Response: ${response.status} - ${JSON.stringify(response.data).substring(0, 100)}...`);
    }
  } catch (error) {
    console.log('❌ WhatsApp Gateway connection failed');
    console.log(`   Error: ${error.message}`);
  }
}

// Test 3: Session Creation
async function testSessionCreation() {
  console.log('🔍 Testing session creation...');
  try {
    const sessionId = `test-session-${Date.now()}`;
    const response = await axios.post(`${LARAVEL_BASE_URL}/sessions`, {
      session_id: sessionId
    });
    
    if (response.status === 200 || response.status === 201) {
      tests.sessionCreation = true;
      console.log('✅ Session creation successful');
      console.log(`   Session ID: ${sessionId}`);
      console.log(`   Response: ${response.status} - ${JSON.stringify(response.data).substring(0, 100)}...`);
      
      // Store session ID for other tests
      global.testSessionId = sessionId;
    }
  } catch (error) {
    console.log('❌ Session creation failed');
    console.log(`   Error: ${error.message}`);
    if (error.response) {
      console.log(`   Response: ${error.response.status} - ${JSON.stringify(error.response.data).substring(0, 100)}...`);
    }
  }
}

// Test 4: Session List
async function testSessionList() {
  console.log('🔍 Testing session list retrieval...');
  try {
    const response = await axios.get(`${LARAVEL_BASE_URL}/sessions`);
    if (response.status === 200) {
      tests.sessionList = true;
      console.log('✅ Session list retrieval successful');
      const sessions = response.data.data?.sessions || response.data.sessions || [];
      console.log(`   Found ${sessions.length} session(s)`);
    }
  } catch (error) {
    console.log('❌ Session list retrieval failed');
    console.log(`   Error: ${error.message}`);
  }
}

// Test 5: Session Detail
async function testSessionDetail() {
  console.log('🔍 Testing session detail retrieval...');
  if (!global.testSessionId) {
    console.log('⚠️  Skipping session detail test (no session ID available)');
    return;
  }
  
  try {
    const response = await axios.get(`${LARAVEL_BASE_URL}/sessions/${global.testSessionId}`);
    if (response.status === 200) {
      tests.sessionDetail = true;
      console.log('✅ Session detail retrieval successful');
      console.log(`   Session: ${global.testSessionId}`);
    }
  } catch (error) {
    console.log('❌ Session detail retrieval failed');
    console.log(`   Error: ${error.message}`);
    if (error.response && error.response.status === 404) {
      console.log('   Note: This might be expected if session was not fully created');
    }
  }
}

// Test 6: Message Endpoint
async function testMessageEndpoint() {
  console.log('🔍 Testing message endpoint...');
  if (!global.testSessionId) {
    console.log('⚠️  Skipping message endpoint test (no session ID available)');
    return;
  }
  
  try {
    const response = await axios.get(`${LARAVEL_BASE_URL}/sessions/${global.testSessionId}/messages`);
    if (response.status === 200) {
      tests.messageEndpoint = true;
      console.log('✅ Message endpoint accessible');
      const messages = response.data.data?.messages || response.data.messages || [];
      console.log(`   Found ${messages.length} message(s)`);
    }
  } catch (error) {
    console.log('❌ Message endpoint failed');
    console.log(`   Error: ${error.message}`);
  }
}

// Test 7: Contact Endpoint
async function testContactEndpoint() {
  console.log('🔍 Testing contact endpoint...');
  if (!global.testSessionId) {
    console.log('⚠️  Skipping contact endpoint test (no session ID available)');
    return;
  }
  
  try {
    const response = await axios.get(`${LARAVEL_BASE_URL}/sessions/${global.testSessionId}/contacts`);
    if (response.status === 200) {
      tests.contactEndpoint = true;
      console.log('✅ Contact endpoint accessible');
      const contacts = response.data.data?.contacts || response.data.contacts || [];
      console.log(`   Found ${contacts.length} contact(s)`);
    }
  } catch (error) {
    console.log('❌ Contact endpoint failed');
    console.log(`   Error: ${error.message}`);
  }
}

// Run all tests
async function runAllTests() {
  console.log('Starting API integration tests...\n');
  
  await testLaravelConnection();
  console.log('');
  
  await testGatewayConnection();
  console.log('');
  
  await testSessionCreation();
  console.log('');
  
  await testSessionList();
  console.log('');
  
  await testSessionDetail();
  console.log('');
  
  await testMessageEndpoint();
  console.log('');
  
  await testContactEndpoint();
  console.log('');
  
  // Summary
  console.log('📊 TEST SUMMARY');
  console.log('===============');
  
  const totalTests = Object.keys(tests).length;
  const passedTests = Object.values(tests).filter(Boolean).length;
  const percentage = Math.round((passedTests / totalTests) * 100);
  
  Object.entries(tests).forEach(([test, passed]) => {
    const status = passed ? '✅' : '❌';
    const result = passed ? 'PASS' : 'FAIL';
    console.log(`${status} ${test}: ${result}`);
  });
  
  console.log(`\n🎯 Overall Score: ${passedTests}/${totalTests} (${percentage}%)`);
  
  if (percentage === 100) {
    console.log('🎉 ALL TESTS PASSED! WhatsApp integration is working perfectly.');
  } else if (percentage >= 70) {
    console.log('✅ MOST TESTS PASSED! Integration is mostly functional.');
  } else {
    console.log('⚠️  SEVERAL TESTS FAILED! Please check the setup.');
  }
  
  console.log('\n🔧 Next Steps:');
  console.log('1. Open http://127.0.0.1:8000 to access the dashboard');
  console.log('2. Create a new WhatsApp session');
  console.log('3. Scan the QR code with your phone');
  console.log('4. Start sending messages!');
  
  return percentage;
}

// Run tests
runAllTests()
  .then(score => {
    process.exit(score >= 70 ? 0 : 1);
  })
  .catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
