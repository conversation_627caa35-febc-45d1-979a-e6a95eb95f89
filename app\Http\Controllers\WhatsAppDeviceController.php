<?php

namespace App\Http\Controllers;

use App\Models\WhatsAppDevice;
use App\Models\WhatsAppSession;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class WhatsAppDeviceController extends Controller
{
    private string $gatewayUrl;

    public function __construct()
    {
        $this->gatewayUrl = config('whatsapp.gateway_url', 'http://localhost:3000');
    }

    /**
     * Display device management page
     */
    public function index()
    {
        $devices = WhatsAppDevice::with('sessions')->orderBy('created_at', 'desc')->get();
        
        return view('whatsapp.devices.index', compact('devices'));
    }

    /**
     * Show the form for creating a new device
     */
    public function create()
    {
        return view('whatsapp.devices.create');
    }

    /**
     * Store a newly created device
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'device_name' => 'required|string|max:255',
            'connection_method' => 'required|in:qr_code,auth_code',
        ]);

        try {
            $deviceId = 'device-' . Str::random(10) . '-' . time();
            
            $device = WhatsAppDevice::create([
                'device_name' => $request->device_name,
                'device_id' => $deviceId,
                'connection_method' => $request->connection_method,
                'status' => 'pending',
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Device created successfully',
                'device' => $device,
                'redirect_url' => route('whatsapp.devices.connect', $device->device_id)
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to create device', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create device: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show device connection interface
     */
    public function connect(string $deviceId)
    {
        $device = WhatsAppDevice::where('device_id', $deviceId)->firstOrFail();
        
        if ($device->status === 'connected') {
            return redirect()->route('whatsapp.devices.show', $deviceId)
                ->with('info', 'Device is already connected');
        }

        // Update device status to connecting
        $device->update(['status' => 'connecting']);

        return view('whatsapp.devices.connect', compact('device'));
    }

    /**
     * Start device connection process
     */
    public function startConnection(Request $request, string $deviceId): JsonResponse
    {
        $device = WhatsAppDevice::where('device_id', $deviceId)->firstOrFail();
        
        try {
            // Generate session ID for this device
            $sessionId = $device->session_id ?: 'session-' . $deviceId;
            
            // Create session in WhatsApp Gateway
            $response = Http::post("{$this->gatewayUrl}/api/sessions", [
                'sessionId' => $sessionId
            ]);

            if (!$response->successful()) {
                throw new \Exception('Failed to create session in gateway');
            }

            // Update device with session ID
            $device->update([
                'session_id' => $sessionId,
                'status' => 'connecting'
            ]);

            // Create session record
            WhatsAppSession::updateOrCreate(
                ['session_id' => $sessionId],
                [
                    'device_id' => $device->id,
                    'status' => 'connecting'
                ]
            );

            return response()->json([
                'success' => true,
                'session_id' => $sessionId,
                'device_id' => $deviceId
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to start device connection', [
                'device_id' => $deviceId,
                'error' => $e->getMessage()
            ]);

            $device->update(['status' => 'failed']);

            return response()->json([
                'success' => false,
                'message' => 'Failed to start connection: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show device details
     */
    public function show(string $deviceId)
    {
        $device = WhatsAppDevice::with('sessions')->where('device_id', $deviceId)->firstOrFail();
        
        return view('whatsapp.devices.show', compact('device'));
    }

    /**
     * Update device status
     */
    public function updateStatus(Request $request, string $deviceId): JsonResponse
    {
        $request->validate([
            'status' => 'required|in:pending,connecting,connected,disconnected,failed',
            'phone_number' => 'nullable|string',
            'display_name' => 'nullable|string',
        ]);

        try {
            $device = WhatsAppDevice::where('device_id', $deviceId)->firstOrFail();
            
            $updateData = ['status' => $request->status];
            
            if ($request->phone_number) {
                $updateData['phone_number'] = $request->phone_number;
            }
            
            if ($request->display_name) {
                $updateData['display_name'] = $request->display_name;
            }
            
            if ($request->status === 'connected') {
                $updateData['last_connected_at'] = now();
            }

            $device->update($updateData);

            return response()->json([
                'success' => true,
                'message' => 'Device status updated successfully',
                'device' => $device->fresh()
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update device status', [
                'device_id' => $deviceId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update device status'
            ], 500);
        }
    }

    /**
     * Delete a device
     */
    public function destroy(string $deviceId): JsonResponse
    {
        try {
            $device = WhatsAppDevice::where('device_id', $deviceId)->firstOrFail();
            
            // Delete session from gateway if exists
            if ($device->session_id) {
                Http::delete("{$this->gatewayUrl}/api/sessions/{$device->session_id}");
            }
            
            // Delete device and related sessions
            $device->sessions()->delete();
            $device->delete();

            return response()->json([
                'success' => true,
                'message' => 'Device deleted successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to delete device', [
                'device_id' => $deviceId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete device'
            ], 500);
        }
    }

    /**
     * Get device status for API
     */
    public function getStatus(string $deviceId): JsonResponse
    {
        try {
            $device = WhatsAppDevice::where('device_id', $deviceId)->firstOrFail();
            
            return response()->json([
                'success' => true,
                'device' => $device
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Device not found'
            ], 404);
        }
    }
}
