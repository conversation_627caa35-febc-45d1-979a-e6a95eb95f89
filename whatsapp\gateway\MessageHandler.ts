import { WAMessage, getContentType, proto, jidDecode } from 'baileys';
import { DatabaseManager } from '../database/DatabaseManager';

export class MessageHandler {
  private dbManager: DatabaseManager;

  constructor(dbManager: DatabaseManager) {
    this.dbManager = dbManager;
  }

  async processIncomingMessage(sessionId: string, message: WAMessage): Promise<void> {
    try {
      const messageType = getContentType(message.message);
      const messageContent = message.message?.[messageType as keyof proto.IMessage];
      
      const messageData = {
        message_id: message.key.id!,
        session_id: sessionId,
        from_jid: message.key.remoteJid!,
        from_me: message.key.fromMe || false,
        participant: message.key.participant || null,
        message_type: messageType || 'unknown',
        text_content: this.extractTextContent(messageContent),
        media_url: null,
        media_type: null,
        media_size: null,
        timestamp: new Date(Number(message.messageTimestamp) * 1000),
        status: 'received',
        raw_message: JSON.stringify(message)
      };

      // Handle media messages
      if (this.isMediaMessage(messageType)) {
        messageData.media_type = this.getMediaType(messageType);
        messageData.media_size = this.getMediaSize(messageContent);
      }

      await this.dbManager.storeMessage(messageData);

      // Extract contact info if not exists
      await this.extractAndStoreContact(sessionId, message);

    } catch (error) {
      console.error('Error processing incoming message:', error);
    }
  }

  async storeSentMessage(sessionId: string, to: string, messageData: any, sentMessage: any): Promise<void> {
    try {
      const messageRecord = {
        message_id: sentMessage.key.id,
        session_id: sessionId,
        from_jid: to,
        from_me: true,
        participant: null,
        message_type: messageData.type,
        text_content: messageData.text || messageData.caption || null,
        media_url: messageData.url || null,
        media_type: messageData.type !== 'text' ? messageData.type : null,
        media_size: null,
        timestamp: new Date(Number(sentMessage.messageTimestamp) * 1000),
        status: 'sent',
        raw_message: JSON.stringify(sentMessage)
      };

      await this.dbManager.storeMessage(messageRecord);
    } catch (error) {
      console.error('Error storing sent message:', error);
    }
  }

  async handleMessageUpdate(sessionId: string, update: any): Promise<void> {
    try {
      const { key, update: messageUpdate } = update;
      
      if (messageUpdate.status) {
        await this.dbManager.updateMessageStatus(key.id, messageUpdate.status);
      }
    } catch (error) {
      console.error('Error handling message update:', error);
    }
  }

  private extractTextContent(messageContent: any): string | null {
    if (!messageContent) return null;

    // Handle different message types
    if (messageContent.text) return messageContent.text;
    if (messageContent.caption) return messageContent.caption;
    if (messageContent.conversation) return messageContent.conversation;
    if (messageContent.extendedTextMessage?.text) return messageContent.extendedTextMessage.text;
    
    return null;
  }

  private isMediaMessage(messageType: string | null): boolean {
    if (!messageType) return false;
    
    const mediaTypes = [
      'imageMessage',
      'videoMessage',
      'audioMessage',
      'documentMessage',
      'stickerMessage'
    ];
    
    return mediaTypes.includes(messageType);
  }

  private getMediaType(messageType: string | null): string | null {
    if (!messageType) return null;
    
    const typeMap: { [key: string]: string } = {
      'imageMessage': 'image',
      'videoMessage': 'video',
      'audioMessage': 'audio',
      'documentMessage': 'document',
      'stickerMessage': 'sticker'
    };
    
    return typeMap[messageType] || null;
  }

  private getMediaSize(messageContent: any): number | null {
    if (!messageContent) return null;
    
    return messageContent.fileLength || null;
  }

  private async extractAndStoreContact(sessionId: string, message: WAMessage): Promise<void> {
    try {
      const jid = message.key.remoteJid!;
      const participant = message.key.participant;
      
      // Extract contact from individual chat
      if (!jid.includes('@g.us')) {
        const decoded = jidDecode(jid);
        if (decoded) {
          await this.dbManager.storeContact({
            session_id: sessionId,
            jid: jid,
            phone_number: decoded.user,
            name: message.pushName || null,
            is_group: false,
            last_seen: new Date()
          });
        }
      }
      
      // Extract contact from group chat
      if (participant) {
        const decoded = jidDecode(participant);
        if (decoded) {
          await this.dbManager.storeContact({
            session_id: sessionId,
            jid: participant,
            phone_number: decoded.user,
            name: message.pushName || null,
            is_group: false,
            last_seen: new Date()
          });
        }
      }
      
      // Store group info if it's a group message
      if (jid.includes('@g.us')) {
        await this.dbManager.storeContact({
          session_id: sessionId,
          jid: jid,
          phone_number: null,
          name: null, // Group name would need to be fetched separately
          is_group: true,
          last_seen: new Date()
        });
      }
      
    } catch (error) {
      console.error('Error extracting contact info:', error);
    }
  }
}