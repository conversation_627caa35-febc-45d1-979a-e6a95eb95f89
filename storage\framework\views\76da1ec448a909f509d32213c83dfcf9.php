<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Gateway Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen">
        <!-- Navigation -->
        <nav class="bg-green-600 text-white p-4">
            <div class="container mx-auto flex justify-between items-center">
                <h1 class="text-2xl font-bold">
                    <i class="fab fa-whatsapp mr-2"></i>
                    WhatsApp Gateway
                </h1>
                <div class="space-x-4">
                    <a href="<?php echo e(route('whatsapp.dashboard')); ?>" class="hover:text-green-200">Dashboard</a>
                    <a href="<?php echo e(route('whatsapp.devices.index')); ?>" class="hover:text-green-200">Device Management</a>
                    <a href="<?php echo e(route('whatsapp.sessions')); ?>" class="hover:text-green-200">Sessions</a>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="container mx-auto p-6">
            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 text-green-600">
                            <i class="fas fa-mobile-alt text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600">Total Sessions</p>
                            <p class="text-2xl font-semibold" id="total-sessions"><?php echo e(count($sessions)); ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                            <i class="fas fa-check-circle text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600">Connected</p>
                            <p class="text-2xl font-semibold text-green-600" id="connected-sessions">
                                <?php echo e(collect($sessions)->where('status', 'connected')->count()); ?>

                            </p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                            <i class="fas fa-qrcode text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600">Pending QR</p>
                            <p class="text-2xl font-semibold text-yellow-600" id="qr-sessions">
                                <?php echo e(collect($sessions)->where('status', 'qr')->count()); ?>

                            </p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-red-100 text-red-600">
                            <i class="fas fa-times-circle text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600">Disconnected</p>
                            <p class="text-2xl font-semibold text-red-600" id="disconnected-sessions">
                                <?php echo e(collect($sessions)->where('status', 'disconnected')->count()); ?>

                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow p-6 mb-8">
                <h2 class="text-xl font-semibold mb-4">Quick Actions</h2>
                <div class="flex flex-wrap gap-4">
                    <a href="<?php echo e(route('whatsapp.devices.create')); ?>" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                        <i class="fas fa-plus mr-2"></i>Add Device
                    </a>
                    <a href="<?php echo e(route('whatsapp.devices.index')); ?>" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                        <i class="fas fa-mobile-alt mr-2"></i>Device Management
                    </a>
                    <a href="<?php echo e(route('whatsapp.connect.device')); ?>" class="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700">
                        <i class="fas fa-qrcode mr-2"></i>Quick Connect Device
                    </a>
                    <button onclick="refreshSessions()" class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">
                        <i class="fas fa-sync mr-2"></i>Refresh Dashboard
                    </button>
                    <a href="<?php echo e(route('whatsapp.sessions')); ?>" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                        <i class="fas fa-list mr-2"></i>Manage Sessions
                    </a>
                </div>
            </div>

            <!-- Recent Sessions -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b">
                    <h2 class="text-xl font-semibold">Recent Sessions</h2>
                </div>
                <div class="p-6">
                    <?php if(count($sessions) > 0): ?>
                        <div class="overflow-x-auto">
                            <table class="min-w-full table-auto">
                                <thead>
                                    <tr class="bg-gray-50">
                                        <th class="px-4 py-2 text-left">Session ID</th>
                                        <th class="px-4 py-2 text-left">Status</th>
                                        <th class="px-4 py-2 text-left">Phone Number</th>
                                        <th class="px-4 py-2 text-left">Name</th>
                                        <th class="px-4 py-2 text-left">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="sessions-table">
                                    <?php $__currentLoopData = $sessions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $session): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr class="border-b hover:bg-gray-50" data-session-id="<?php echo e($session['sessionId']); ?>">
                                            <td class="px-4 py-2 font-mono"><?php echo e($session['sessionId']); ?></td>
                                            <td class="px-4 py-2">
                                                <span class="px-2 py-1 rounded-full text-xs
                                                    <?php if($session['status'] === 'connected'): ?> bg-green-100 text-green-800
                                                    <?php elseif($session['status'] === 'qr'): ?> bg-yellow-100 text-yellow-800
                                                    <?php elseif($session['status'] === 'connecting'): ?> bg-blue-100 text-blue-800
                                                    <?php else: ?> bg-red-100 text-red-800
                                                    <?php endif; ?>">
                                                    <?php echo e(ucfirst($session['status'])); ?>

                                                </span>
                                            </td>
                                            <td class="px-4 py-2"><?php echo e($session['info']['id'] ?? 'N/A'); ?></td>
                                            <td class="px-4 py-2"><?php echo e($session['info']['name'] ?? 'N/A'); ?></td>
                                            <td class="px-4 py-2">
                                                <div class="flex space-x-2">
                                                    <a href="<?php echo e(route('whatsapp.session.detail', $session['sessionId'])); ?>" 
                                                       class="text-blue-600 hover:text-blue-800">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <?php if($session['status'] === 'connected'): ?>
                                                        <a href="<?php echo e(route('whatsapp.send.message', $session['sessionId'])); ?>" 
                                                           class="text-green-600 hover:text-green-800">
                                                            <i class="fas fa-paper-plane"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    <button onclick="deleteSession('<?php echo e($session['sessionId']); ?>')" 
                                                            class="text-red-600 hover:text-red-800">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-8">
                            <i class="fas fa-mobile-alt text-4xl text-gray-400 mb-4"></i>
                            <p class="text-gray-600">No sessions found. Create your first session to get started.</p>
                            <button onclick="createSession()" class="mt-4 bg-green-600 text-white px-6 py-2 rounded hover:bg-green-700">
                                Create Session
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Session Modal -->
    <div id="createSessionModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg p-6 w-full max-w-md">
                <h3 class="text-lg font-semibold mb-4">Create New Session</h3>
                <form id="createSessionForm">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Session ID</label>
                        <input type="text" id="sessionId" class="w-full border rounded px-3 py-2" 
                               placeholder="Enter unique session ID" required>
                    </div>
                    <div class="flex justify-end space-x-4">
                        <button type="button" onclick="closeCreateSessionModal()" 
                                class="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
                        <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                            Create Session
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Socket.IO connection
        const socket = io('<?php echo e(config("whatsapp.socket_url", "http://localhost:3000")); ?>');

        socket.on('connect', () => {
            console.log('Connected to WhatsApp Gateway');
        });

        socket.on('session-status', (data) => {
            updateSessionStatus(data.sessionId, data.status, data.info);
        });

        socket.on('qr-updated', (data) => {
            console.log('QR updated for session:', data.sessionId);
        });

        // Functions
        function createSession() {
            document.getElementById('createSessionModal').classList.remove('hidden');
        }

        function closeCreateSessionModal() {
            document.getElementById('createSessionModal').classList.add('hidden');
            document.getElementById('createSessionForm').reset();
        }

        document.getElementById('createSessionForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const sessionId = document.getElementById('sessionId').value;

            try {
                const response = await fetch('/api/whatsapp/sessions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                    },
                    body: JSON.stringify({ session_id: sessionId })
                });

                const result = await response.json();

                if (result.success) {
                    closeCreateSessionModal();
                    location.reload();
                } else {
                    alert('Failed to create session: ' + (result.message || 'Unknown error'));
                }
            } catch (error) {
                alert('Error creating session: ' + error.message);
            }
        });

        async function deleteSession(sessionId) {
            if (!confirm('Are you sure you want to delete this session?')) return;

            try {
                const response = await fetch(`/api/whatsapp/sessions/${sessionId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    location.reload();
                } else {
                    alert('Failed to delete session: ' + (result.message || 'Unknown error'));
                }
            } catch (error) {
                alert('Error deleting session: ' + error.message);
            }
        }

        function refreshSessions() {
            location.reload();
        }

        function updateSessionStatus(sessionId, status, info) {
            const row = document.querySelector(`tr[data-session-id="${sessionId}"]`);
            if (row) {
                const statusCell = row.querySelector('td:nth-child(2) span');
                const phoneCell = row.querySelector('td:nth-child(3)');
                const nameCell = row.querySelector('td:nth-child(4)');

                // Update status
                statusCell.textContent = status.charAt(0).toUpperCase() + status.slice(1);
                statusCell.className = 'px-2 py-1 rounded-full text-xs ';
                
                if (status === 'connected') {
                    statusCell.className += 'bg-green-100 text-green-800';
                } else if (status === 'qr') {
                    statusCell.className += 'bg-yellow-100 text-yellow-800';
                } else if (status === 'connecting') {
                    statusCell.className += 'bg-blue-100 text-blue-800';
                } else {
                    statusCell.className += 'bg-red-100 text-red-800';
                }

                // Update info if available
                if (info) {
                    phoneCell.textContent = info.id || 'N/A';
                    nameCell.textContent = info.name || 'N/A';
                }
            }
        }
    </script>
</body>
</html><?php /**PATH C:\laragon\www\larawamd\resources\views/whatsapp/dashboard.blade.php ENDPATH**/ ?>