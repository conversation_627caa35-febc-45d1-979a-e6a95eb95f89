<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connect WhatsApp Device - QR Code Scanner</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .qr-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .qr-scanner {
            border: 3px solid #10b981;
            border-radius: 20px;
            box-shadow: 0 0 30px rgba(16, 185, 129, 0.3);
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-connecting { background-color: #f59e0b; animation: pulse 1s infinite; }
        .status-qr { background-color: #3b82f6; animation: pulse 1s infinite; }
        .status-connected { background-color: #10b981; }
        .status-disconnected { background-color: #ef4444; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="container mx-auto px-6 py-4">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-4">
                    <a href="{{ route('whatsapp.dashboard') }}" class="text-gray-600 hover:text-gray-800">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                    <div class="h-6 w-px bg-gray-300"></div>
                    <h1 class="text-xl font-semibold text-gray-800">
                        <i class="fab fa-whatsapp text-green-500 mr-2"></i>
                        Connect WhatsApp Device
                    </h1>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center">
                        <span class="status-indicator" id="connection-indicator"></span>
                        <span class="text-sm text-gray-600" id="connection-status">Initializing...</span>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mx-auto px-6 py-8">
        <!-- Progress Steps -->
        <div class="mb-8">
            <div class="flex items-center justify-center space-x-4">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                        1
                    </div>
                    <span class="ml-2 text-sm font-medium text-green-600">Create Session</span>
                </div>
                <div class="w-16 h-1 bg-green-500"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium" id="step-2">
                        2
                    </div>
                    <span class="ml-2 text-sm font-medium text-blue-600" id="step-2-text">Generate QR Code</span>
                </div>
                <div class="w-16 h-1 bg-gray-300" id="progress-bar"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-medium" id="step-3">
                        3
                    </div>
                    <span class="ml-2 text-sm font-medium text-gray-500" id="step-3-text">Device Connected</span>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="max-w-4xl mx-auto">
            <!-- Session Info Card -->
            <div class="bg-white rounded-xl shadow-sm border mb-6 fade-in">
                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h2 class="text-lg font-semibold text-gray-800">Session Information</h2>
                            <p class="text-sm text-gray-600 mt-1">Session ID: <span class="font-mono bg-gray-100 px-2 py-1 rounded" id="session-id">{{ $sessionId ?? 'Generating...' }}</span></p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm text-gray-600">Status</p>
                            <p class="font-semibold" id="session-status">Initializing</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- QR Code Section -->
                <div class="bg-white rounded-xl shadow-sm border fade-in">
                    <div class="p-6">
                        <div class="text-center">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">
                                <i class="fas fa-qrcode text-blue-500 mr-2"></i>
                                QR Code Scanner
                            </h3>
                            
                            <!-- QR Code Display -->
                            <div class="qr-container p-8 rounded-xl mb-6">
                                <div id="qr-loading" class="text-center">
                                    <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-white mb-4"></div>
                                    <p class="text-white">Generating QR Code...</p>
                                </div>
                                
                                <div id="qr-display" class="hidden text-center">
                                    <div class="bg-white p-4 rounded-xl qr-scanner inline-block">
                                        <img id="qr-image" src="" alt="WhatsApp QR Code" class="w-64 h-64 mx-auto">
                                    </div>
                                    <p class="text-white mt-4 text-sm">Scan this QR code with your WhatsApp mobile app</p>
                                </div>

                                <div id="qr-error" class="hidden text-center">
                                    <i class="fas fa-exclamation-triangle text-red-300 text-4xl mb-4"></i>
                                    <p class="text-white">Failed to generate QR code</p>
                                    <button onclick="retryQRGeneration()" class="mt-4 bg-white text-blue-600 px-4 py-2 rounded-lg hover:bg-gray-100">
                                        Try Again
                                    </button>
                                </div>
                            </div>

                            <!-- QR Code Actions -->
                            <div class="space-y-3">
                                <button onclick="refreshQR()" class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                                    <i class="fas fa-sync mr-2"></i>Refresh QR Code
                                </button>
                                <button onclick="downloadQR()" class="w-full bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors" id="download-btn" disabled>
                                    <i class="fas fa-download mr-2"></i>Download QR Code
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Instructions Section -->
                <div class="space-y-6">
                    <!-- Connection Instructions -->
                    <div class="bg-white rounded-xl shadow-sm border fade-in">
                        <div class="p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">
                                <i class="fas fa-mobile-alt text-green-500 mr-2"></i>
                                How to Connect
                            </h3>
                            
                            <div class="space-y-4">
                                <div class="flex items-start space-x-3">
                                    <div class="w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0 mt-0.5">
                                        1
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-800">Open WhatsApp on your phone</p>
                                        <p class="text-sm text-gray-600">Make sure you have the latest version installed</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-start space-x-3">
                                    <div class="w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0 mt-0.5">
                                        2
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-800">Go to Settings → Linked Devices</p>
                                        <p class="text-sm text-gray-600">Tap the three dots menu, then "Linked Devices"</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-start space-x-3">
                                    <div class="w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0 mt-0.5">
                                        3
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-800">Tap "Link a Device"</p>
                                        <p class="text-sm text-gray-600">This will open your camera to scan QR codes</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-start space-x-3">
                                    <div class="w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0 mt-0.5">
                                        4
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-800">Scan the QR code</p>
                                        <p class="text-sm text-gray-600">Point your camera at the QR code on the left</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Alternative Methods -->
                    <div class="bg-white rounded-xl shadow-sm border fade-in">
                        <div class="p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">
                                <i class="fas fa-key text-orange-500 mr-2"></i>
                                Alternative Connection
                            </h3>
                            
                            <div class="space-y-3">
                                <p class="text-sm text-gray-600">If QR code scanning doesn't work, you can also:</p>
                                
                                <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
                                    <div class="flex items-center">
                                        <i class="fas fa-info-circle text-orange-500 mr-2"></i>
                                        <span class="text-sm font-medium text-orange-800">Authentication Code Method</span>
                                    </div>
                                    <p class="text-sm text-orange-700 mt-2">
                                        Some devices support pairing codes. Check your WhatsApp settings for "Link with phone number" option.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Connection Status -->
                    <div class="bg-white rounded-xl shadow-sm border fade-in">
                        <div class="p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">
                                <i class="fas fa-wifi text-blue-500 mr-2"></i>
                                Connection Status
                            </h3>
                            
                            <div id="status-messages" class="space-y-2">
                                <div class="flex items-center text-sm text-gray-600">
                                    <i class="fas fa-circle text-gray-400 mr-2"></i>
                                    Waiting for connection...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Success Modal -->
    <div id="successModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-xl p-8 w-full max-w-md text-center">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-check text-green-500 text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-2">Device Connected!</h3>
                <p class="text-gray-600 mb-6">Your WhatsApp device has been successfully connected.</p>
                <div class="space-y-3">
                    <button onclick="goToDashboard()" class="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700">
                        Go to Dashboard
                    </button>
                    <button onclick="sendTestMessage()" class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700">
                        Send Test Message
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Configuration
        const sessionId = '{{ $sessionId ?? '' }}' || 'device-' + Date.now();
        const socketUrl = '{{ config("whatsapp.socket_url", "http://localhost:3000") }}';
        
        // Socket.IO connection
        const socket = io(socketUrl);
        
        // State management
        let currentStatus = 'initializing';
        let qrCodeData = null;
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateSessionId();
            createSession();
        });
        
        // Socket event handlers
        socket.on('connect', () => {
            updateConnectionStatus('connected', 'Connected to Gateway');
            addStatusMessage('Connected to WhatsApp Gateway', 'success');
        });
        
        socket.on('disconnect', () => {
            updateConnectionStatus('disconnected', 'Disconnected from Gateway');
            addStatusMessage('Disconnected from Gateway', 'error');
        });
        
        socket.on('qr-updated', (data) => {
            if (data.sessionId === sessionId) {
                displayQRCode(data.qr);
                updateStatus('qr', 'QR Code Ready');
                addStatusMessage('QR Code generated - ready to scan', 'info');
            }
        });
        
        socket.on('session-status', (data) => {
            if (data.sessionId === sessionId) {
                handleSessionStatus(data.status, data.info);
            }
        });
        
        socket.on('session-error', (data) => {
            if (data.sessionId === sessionId) {
                showQRError();
                addStatusMessage('Error: ' + data.error, 'error');
            }
        });
        
        // Functions
        function updateSessionId() {
            document.getElementById('session-id').textContent = sessionId;
        }
        
        async function createSession() {
            try {
                updateStatus('connecting', 'Creating Session');
                addStatusMessage('Creating WhatsApp session...', 'info');
                
                const response = await fetch('/api/whatsapp/sessions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({ session_id: sessionId })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addStatusMessage('Session created successfully', 'success');
                    updateStatus('qr', 'Generating QR Code');
                } else {
                    throw new Error(result.message || 'Failed to create session');
                }
            } catch (error) {
                addStatusMessage('Failed to create session: ' + error.message, 'error');
                showQRError();
            }
        }
        
        function displayQRCode(qrData) {
            qrCodeData = qrData;
            document.getElementById('qr-loading').classList.add('hidden');
            document.getElementById('qr-error').classList.add('hidden');
            document.getElementById('qr-display').classList.remove('hidden');
            document.getElementById('qr-image').src = qrData;
            document.getElementById('download-btn').disabled = false;
        }
        
        function showQRError() {
            document.getElementById('qr-loading').classList.add('hidden');
            document.getElementById('qr-display').classList.add('hidden');
            document.getElementById('qr-error').classList.remove('hidden');
        }
        
        function updateStatus(status, text) {
            currentStatus = status;
            document.getElementById('session-status').textContent = text;
            
            // Update progress steps
            const step2 = document.getElementById('step-2');
            const step3 = document.getElementById('step-3');
            const progressBar = document.getElementById('progress-bar');
            
            if (status === 'qr') {
                step2.className = 'w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-medium';
                document.getElementById('step-2-text').className = 'ml-2 text-sm font-medium text-green-600';
            } else if (status === 'connected') {
                step2.className = 'w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-medium';
                step3.className = 'w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-medium';
                progressBar.className = 'w-16 h-1 bg-green-500';
                document.getElementById('step-3-text').className = 'ml-2 text-sm font-medium text-green-600';
            }
        }
        
        function updateConnectionStatus(status, text) {
            const indicator = document.getElementById('connection-indicator');
            const statusText = document.getElementById('connection-status');
            
            indicator.className = 'status-indicator status-' + status;
            statusText.textContent = text;
        }
        
        function addStatusMessage(message, type) {
            const container = document.getElementById('status-messages');
            const messageEl = document.createElement('div');
            messageEl.className = 'flex items-center text-sm';
            
            let iconClass = 'fas fa-circle text-gray-400';
            let textClass = 'text-gray-600';
            
            if (type === 'success') {
                iconClass = 'fas fa-check-circle text-green-500';
                textClass = 'text-green-700';
            } else if (type === 'error') {
                iconClass = 'fas fa-times-circle text-red-500';
                textClass = 'text-red-700';
            } else if (type === 'info') {
                iconClass = 'fas fa-info-circle text-blue-500';
                textClass = 'text-blue-700';
            }
            
            messageEl.innerHTML = `
                <i class="${iconClass} mr-2"></i>
                <span class="${textClass}">${message}</span>
                <span class="text-xs text-gray-400 ml-auto">${new Date().toLocaleTimeString()}</span>
            `;
            
            container.appendChild(messageEl);
            container.scrollTop = container.scrollHeight;
        }
        
        function handleSessionStatus(status, info) {
            if (status === 'connected') {
                updateStatus('connected', 'Device Connected');
                addStatusMessage('Device connected successfully!', 'success');
                
                if (info) {
                    addStatusMessage(`Phone: ${info.id || 'Unknown'}`, 'info');
                    addStatusMessage(`Name: ${info.name || 'Unknown'}`, 'info');
                }
                
                setTimeout(() => {
                    document.getElementById('successModal').classList.remove('hidden');
                }, 1000);
            } else if (status === 'qr') {
                updateStatus('qr', 'Waiting for QR Scan');
                addStatusMessage('QR code ready - waiting for scan', 'info');
            } else if (status === 'connecting') {
                updateStatus('connecting', 'Connecting...');
                addStatusMessage('Connecting to WhatsApp...', 'info');
            } else if (status === 'disconnected') {
                updateStatus('disconnected', 'Disconnected');
                addStatusMessage('Device disconnected', 'error');
            }
        }
        
        function refreshQR() {
            document.getElementById('qr-display').classList.add('hidden');
            document.getElementById('qr-loading').classList.remove('hidden');
            document.getElementById('download-btn').disabled = true;
            addStatusMessage('Refreshing QR code...', 'info');
            createSession();
        }
        
        function retryQRGeneration() {
            refreshQR();
        }
        
        function downloadQR() {
            if (qrCodeData) {
                const link = document.createElement('a');
                link.href = qrCodeData;
                link.download = `whatsapp-qr-${sessionId}.png`;
                link.click();
            }
        }
        
        function goToDashboard() {
            window.location.href = '{{ route("whatsapp.dashboard") }}';
        }
        
        function sendTestMessage() {
            window.location.href = `/whatsapp/sessions/${sessionId}/send`;
        }
    </script>
</body>
</html>
