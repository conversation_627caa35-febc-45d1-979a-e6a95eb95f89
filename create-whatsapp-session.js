#!/usr/bin/env node

/**
 * WhatsApp Session Creator
 * 
 * This script creates a new WhatsApp session and displays the QR code
 * for device connection.
 */

import axios from 'axios';
import { io } from 'socket.io-client';

const LARAVEL_BASE_URL = 'http://127.0.0.1:8000/api/whatsapp';
const GATEWAY_BASE_URL = 'http://localhost:3000';

console.log('📱 WhatsApp Device Connection Setup');
console.log('===================================\n');

// Generate a unique session ID
const sessionId = `device-${Date.now()}`;

console.log(`🆔 Session ID: ${sessionId}`);
console.log('🔄 Creating WhatsApp session...\n');

// Connect to Socket.IO for real-time updates
const socket = io(GATEWAY_BASE_URL);

socket.on('connect', () => {
  console.log('✅ Connected to WhatsApp Gateway Socket.IO server');
});

socket.on('disconnect', () => {
  console.log('❌ Disconnected from Socket.IO server');
});

// Listen for QR code updates
socket.on('qr-updated', (data) => {
  if (data.sessionId === sessionId) {
    console.log('\n📱 QR CODE READY!');
    console.log('==================');
    console.log('🔗 QR Code Data URL:', data.qr.substring(0, 100) + '...');
    console.log('\n📋 Instructions:');
    console.log('1. Open WhatsApp on your phone');
    console.log('2. Go to Settings > Linked Devices');
    console.log('3. Tap "Link a Device"');
    console.log('4. Scan the QR code displayed in your browser');
    console.log('\n🌐 You can also view the QR code at:');
    console.log(`   http://localhost:8000/whatsapp/sessions/${sessionId}`);
  }
});

// Listen for session status updates
socket.on('session-status', (data) => {
  if (data.sessionId === sessionId) {
    console.log(`\n📊 Session Status: ${data.status.toUpperCase()}`);
    
    switch (data.status) {
      case 'connecting':
        console.log('🔄 Connecting to WhatsApp...');
        break;
      case 'qr':
        console.log('📱 QR code generated, waiting for scan...');
        break;
      case 'connected':
        console.log('✅ Device connected successfully!');
        if (data.info) {
          console.log(`📞 Phone: ${data.info.id || 'Unknown'}`);
          console.log(`👤 Name: ${data.info.name || 'Unknown'}`);
        }
        console.log('\n🎉 You can now send and receive messages!');
        console.log(`🌐 Dashboard: http://localhost:8000/whatsapp/sessions/${sessionId}`);
        break;
      case 'disconnected':
        console.log('❌ Device disconnected');
        break;
    }
  }
});

// Listen for incoming messages
socket.on('message-received', (data) => {
  if (data.sessionId === sessionId) {
    console.log('\n📨 New Message Received:');
    console.log(`   From: ${data.message.from}`);
    console.log(`   Type: ${data.message.type}`);
    console.log(`   Content: ${data.message.body || '[Media]'}`);
  }
});

// Listen for errors
socket.on('session-error', (data) => {
  if (data.sessionId === sessionId) {
    console.log(`\n❌ Session Error: ${data.error}`);
  }
});

// Create the session
async function createSession() {
  try {
    console.log('🔄 Creating session via Laravel API...');
    
    const response = await axios.post(`${LARAVEL_BASE_URL}/sessions`, {
      session_id: sessionId
    });
    
    if (response.data.success) {
      console.log('✅ Session created successfully!');
      console.log('⏳ Waiting for QR code generation...');
      console.log('\n💡 Keep this terminal open to see real-time updates');
      console.log('💡 The QR code will appear shortly...');
    } else {
      console.log('❌ Failed to create session:', response.data.message);
      process.exit(1);
    }
    
  } catch (error) {
    console.log('❌ Error creating session:', error.message);
    if (error.response) {
      console.log('   Response:', error.response.status, error.response.data);
    }
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n\n🛑 Shutting down...');
  socket.disconnect();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n\n🛑 Shutting down...');
  socket.disconnect();
  process.exit(0);
});

// Start the process
console.log('🚀 Starting WhatsApp device connection process...\n');
createSession();

// Keep the script running to listen for events
console.log('\n⏳ Listening for WhatsApp events...');
console.log('   Press Ctrl+C to stop\n');
