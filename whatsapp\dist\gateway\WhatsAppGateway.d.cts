export class WhatsAppGateway {
    constructor(io: any, dbManager: any);
    sessions: Map<any, any>;
    io: any;
    dbManager: any;
    logger: any;
    store: NodeCache;
    groupCache: NodeCache;
    createSession(sessionId: any): Promise<void>;
    handleIncomingMessages(sessionId: any, messageUpdate: any): Promise<void>;
    formatMessage(message: any): {
        id: any;
        from: any;
        fromMe: any;
        timestamp: any;
        type: keyof import("baileys").proto.IMessage | undefined;
        body: any;
        hasMedia: boolean;
        participant: any;
    };
    sendMessage(sessionId: any, to: any, message: any): Promise<{
        success: boolean;
        messageId: any;
        timestamp: any;
    }>;
    getSessionStatus(sessionId: any): Promise<any>;
    getAllSessions(): Promise<any[]>;
    deleteSession(sessionId: any): Promise<void>;
    cleanup(): Promise<void>;
}
import NodeCache = require("node-cache");
//# sourceMappingURL=WhatsAppGateway.d.cts.map