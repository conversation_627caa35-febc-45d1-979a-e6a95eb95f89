<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\WhatsAppWebController;
use App\Http\Controllers\WhatsAppDeviceController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return redirect()->route('whatsapp.dashboard');
});

/*
|--------------------------------------------------------------------------
| WhatsApp Web Interface Routes
|--------------------------------------------------------------------------
*/

Route::prefix('whatsapp')->name('whatsapp.')->group(function () {
    Route::get('/dashboard', [WhatsAppWebController::class, 'dashboard'])->name('dashboard');
    Route::get('/sessions', [WhatsAppWebController::class, 'sessions'])->name('sessions');
    Route::get('/sessions/{sessionId}', [WhatsAppWebController::class, 'sessionDetail'])->name('session.detail');
    Route::get('/connect-device/{sessionId?}', [WhatsAppWebController::class, 'connectDevice'])->name('connect.device');
    Route::get('/sessions/{sessionId}/send', [WhatsAppWebController::class, 'sendMessage'])->name('send.message');
    Route::get('/sessions/{sessionId}/messages', [WhatsAppWebController::class, 'messages'])->name('messages');

    // Device Management Routes
    Route::prefix('devices')->name('devices.')->group(function () {
        Route::get('/', [WhatsAppDeviceController::class, 'index'])->name('index');
        Route::get('/create', [WhatsAppDeviceController::class, 'create'])->name('create');
        Route::post('/', [WhatsAppDeviceController::class, 'store'])->name('store');
        Route::get('/{deviceId}', [WhatsAppDeviceController::class, 'show'])->name('show');
        Route::get('/{deviceId}/connect', [WhatsAppDeviceController::class, 'connect'])->name('connect');
        Route::post('/{deviceId}/start-connection', [WhatsAppDeviceController::class, 'startConnection'])->name('start-connection');
        Route::patch('/{deviceId}/status', [WhatsAppDeviceController::class, 'updateStatus'])->name('update-status');
        Route::delete('/{deviceId}', [WhatsAppDeviceController::class, 'destroy'])->name('destroy');
    });
    Route::get('/sessions/{sessionId}/contacts', [WhatsAppWebController::class, 'contacts'])->name('contacts');
});
