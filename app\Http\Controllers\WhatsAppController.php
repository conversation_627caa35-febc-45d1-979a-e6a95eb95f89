<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class WhatsAppController extends Controller
{
    private string $gatewayUrl;

    public function __construct()
    {
        $this->gatewayUrl = config('whatsapp.gateway_url', 'http://localhost:3000');
    }

    /**
     * Create a new WhatsApp session
     */
    public function createSession(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'session_id' => 'required|string|max:255'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $response = Http::post("{$this->gatewayUrl}/api/sessions", [
                'sessionId' => $request->session_id
            ]);

            if ($response->successful()) {
                return response()->json([
                    'success' => true,
                    'data' => $response->json()
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Failed to create session',
                'error' => $response->json()
            ], $response->status());

        } catch (\Exception $e) {
            Log::error('WhatsApp session creation failed', [
                'session_id' => $request->session_id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get all WhatsApp sessions
     */
    public function getSessions(): JsonResponse
    {
        try {
            $response = Http::get("{$this->gatewayUrl}/api/sessions");

            if ($response->successful()) {
                return response()->json([
                    'success' => true,
                    'data' => $response->json()
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch sessions'
            ], $response->status());

        } catch (\Exception $e) {
            Log::error('Failed to fetch WhatsApp sessions', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get specific WhatsApp session
     */
    public function getSession(string $sessionId): JsonResponse
    {
        try {
            $response = Http::get("{$this->gatewayUrl}/api/sessions/{$sessionId}");

            if ($response->successful()) {
                return response()->json([
                    'success' => true,
                    'data' => $response->json()
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Session not found'
            ], $response->status());

        } catch (\Exception $e) {
            Log::error('Failed to fetch WhatsApp session', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Delete WhatsApp session
     */
    public function deleteSession(string $sessionId): JsonResponse
    {
        try {
            $response = Http::delete("{$this->gatewayUrl}/api/sessions/{$sessionId}");

            if ($response->successful()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Session deleted successfully'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete session'
            ], $response->status());

        } catch (\Exception $e) {
            Log::error('Failed to delete WhatsApp session', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Send text message
     */
    public function sendText(Request $request, string $sessionId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'to' => 'required|string',
            'text' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $response = Http::post("{$this->gatewayUrl}/api/sessions/{$sessionId}/send-text", [
                'to' => $request->to,
                'text' => $request->text
            ]);

            if ($response->successful()) {
                return response()->json([
                    'success' => true,
                    'data' => $response->json()
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Failed to send message',
                'error' => $response->json()
            ], $response->status());

        } catch (\Exception $e) {
            Log::error('Failed to send WhatsApp text message', [
                'session_id' => $sessionId,
                'to' => $request->to,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Send media message
     */
    public function sendMedia(Request $request, string $sessionId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'to' => 'required|string',
            'media' => 'required|file|max:51200', // 50MB max
            'type' => 'required|in:image,video,audio,document',
            'caption' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $response = Http::attach(
                'media',
                file_get_contents($request->file('media')->path()),
                $request->file('media')->getClientOriginalName()
            )->post("{$this->gatewayUrl}/api/sessions/{$sessionId}/send-media", [
                'to' => $request->to,
                'type' => $request->type,
                'caption' => $request->caption ?? ''
            ]);

            if ($response->successful()) {
                return response()->json([
                    'success' => true,
                    'data' => $response->json()
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Failed to send media',
                'error' => $response->json()
            ], $response->status());

        } catch (\Exception $e) {
            Log::error('Failed to send WhatsApp media message', [
                'session_id' => $sessionId,
                'to' => $request->to,
                'type' => $request->type,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Send image message
     */
    public function sendImage(Request $request, string $sessionId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'to' => 'required|string',
            'image' => 'required|image|max:10240', // 10MB max
            'caption' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $response = Http::attach(
                'image',
                file_get_contents($request->file('image')->path()),
                $request->file('image')->getClientOriginalName()
            )->post("{$this->gatewayUrl}/api/sessions/{$sessionId}/send-image", [
                'to' => $request->to,
                'caption' => $request->caption ?? ''
            ]);

            if ($response->successful()) {
                return response()->json([
                    'success' => true,
                    'data' => $response->json()
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Failed to send image',
                'error' => $response->json()
            ], $response->status());

        } catch (\Exception $e) {
            Log::error('Failed to send WhatsApp image', [
                'session_id' => $sessionId,
                'to' => $request->to,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get messages for a session
     */
    public function getMessages(Request $request, string $sessionId): JsonResponse
    {
        $limit = $request->get('limit', 50);
        $offset = $request->get('offset', 0);

        try {
            $response = Http::get("{$this->gatewayUrl}/api/sessions/{$sessionId}/messages", [
                'limit' => $limit,
                'offset' => $offset
            ]);

            if ($response->successful()) {
                return response()->json([
                    'success' => true,
                    'data' => $response->json()
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch messages'
            ], $response->status());

        } catch (\Exception $e) {
            Log::error('Failed to fetch WhatsApp messages', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get contacts for a session
     */
    public function getContacts(string $sessionId): JsonResponse
    {
        try {
            $response = Http::get("{$this->gatewayUrl}/api/sessions/{$sessionId}/contacts");

            if ($response->successful()) {
                return response()->json([
                    'success' => true,
                    'data' => $response->json()
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch contacts'
            ], $response->status());

        } catch (\Exception $e) {
            Log::error('Failed to fetch WhatsApp contacts', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error'
            ], 500);
        }
    }
}