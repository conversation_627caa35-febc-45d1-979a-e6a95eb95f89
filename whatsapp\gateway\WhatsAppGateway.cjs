/**
 * WhatsApp Gateway - CommonJS Version
 * 
 * This version uses CommonJS imports to avoid the makeWASocket import issues
 */

const { makeWASocket, DisconnectReason, useMultiFileAuthState, Browsers, fetchLatestBaileysVersion, makeCacheableSignalKeyStore, getContentType } = require('baileys');
const QRCode = require('qrcode');
const pino = require('pino');
const fs = require('fs');
const path = require('path');
const NodeCache = require('node-cache');

class WhatsAppGateway {
  constructor(io, dbManager) {
    this.sessions = new Map();
    this.io = io;
    this.dbManager = dbManager;
    
    // Initialize logger
    this.logger = pino({ 
      level: 'info',
      transport: {
        target: 'pino-pretty',
        options: {
          colorize: true
        }
      }
    });
    
    // Initialize in-memory store for better performance
    this.store = new NodeCache({ stdTTL: 5 * 60, useClones: false });
    this.groupCache = new NodeCache({ stdTTL: 5 * 60, useClones: false });
    
    console.log('✅ WhatsApp Gateway initialized with CommonJS');
  }

  async createSession(sessionId) {
    if (this.sessions.has(sessionId)) {
      throw new Error(`Session ${sessionId} already exists`);
    }

    const sessionDir = path.join(process.cwd(), 'whatsapp', 'sessions', sessionId);
    
    // Ensure session directory exists
    if (!fs.existsSync(sessionDir)) {
      fs.mkdirSync(sessionDir, { recursive: true });
    }

    const session = {
      sessionId,
      socket: null,
      qr: null,
      status: 'connecting',
      info: null
    };

    this.sessions.set(sessionId, session);

    try {
      console.log(`🔄 Creating session: ${sessionId}`);
      
      const { state, saveCreds } = await useMultiFileAuthState(sessionDir);
      
      // Fetch latest Baileys version for better compatibility
      const { version, isLatest } = await fetchLatestBaileysVersion();
      console.log(`✅ Using Baileys version: ${version}, isLatest: ${isLatest}`);
      
      const socket = makeWASocket({
        version,
        auth: {
          creds: state.creds,
          keys: makeCacheableSignalKeyStore(state.keys, this.logger)
        },
        printQRInTerminal: true, // Also print in terminal for debugging
        logger: this.logger,
        browser: Browsers.ubuntu('WhatsApp Gateway'),
        generateHighQualityLinkPreview: true,
        syncFullHistory: false,
        markOnlineOnConnect: true,
        getMessage: async (key) => {
          if (this.store) {
            const msg = this.store.get(`${key.remoteJid}:${key.id}`);
            return msg?.message || undefined;
          }
          return undefined;
        }
      });

      session.socket = socket;
      console.log(`✅ Socket created for session: ${sessionId}`);

      // Handle connection updates
      socket.ev.on('connection.update', async (update) => {
        const { connection, lastDisconnect, qr } = update;
        console.log(`📊 Connection update for ${sessionId}:`, { connection, qr: !!qr });

        if (qr) {
          try {
            console.log(`📱 Generating QR code for session: ${sessionId}`);
            session.qr = await QRCode.toDataURL(qr);
            session.status = 'qr';
            
            // Emit QR code update
            this.io.emit('qr-updated', {
              sessionId,
              qr: session.qr
            });
            
            // Update database
            await this.dbManager.updateSession(sessionId, {
              status: 'qr',
              qr_code: session.qr
            });
            
            console.log(`✅ QR code generated and emitted for session: ${sessionId}`);
          } catch (error) {
            console.error(`❌ Error generating QR code for ${sessionId}:`, error);
            this.io.emit('session-error', {
              sessionId,
              error: 'Failed to generate QR code'
            });
          }
        }

        if (connection === 'close') {
          const shouldReconnect = lastDisconnect?.error?.output?.statusCode !== DisconnectReason.loggedOut;
          
          if (shouldReconnect) {
            console.log(`🔄 Session ${sessionId} disconnected, reconnecting...`);
            session.status = 'connecting';
            this.io.emit('session-status', { sessionId, status: 'connecting' });
            
            // Retry connection after delay
            setTimeout(() => {
              this.createSession(sessionId).catch(console.error);
            }, 5000);
          } else {
            console.log(`❌ Session ${sessionId} logged out`);
            session.status = 'disconnected';
            this.sessions.delete(sessionId);
            this.io.emit('session-status', { sessionId, status: 'disconnected' });
            
            await this.dbManager.updateSession(sessionId, {
              status: 'disconnected'
            });
          }
        } else if (connection === 'open') {
          console.log(`🎉 Session ${sessionId} connected successfully`);
          session.status = 'connected';
          session.info = socket.user;
          session.qr = null;
          
          this.io.emit('session-status', {
            sessionId,
            status: 'connected',
            info: session.info
          });
          
          await this.dbManager.updateSession(sessionId, {
            status: 'connected',
            phone_number: session.info?.id?.split(':')[0] || null,
            name: session.info?.name || null,
            qr_code: null
          });
        }
      });

      // Handle credential updates
      socket.ev.on('creds.update', saveCreds);

      // Handle incoming messages
      socket.ev.on('messages.upsert', async (messageUpdate) => {
        await this.handleIncomingMessages(sessionId, messageUpdate);
      });

      console.log(`⏳ Session ${sessionId} setup complete, waiting for QR scan...`);

    } catch (error) {
      console.error(`❌ Error creating session ${sessionId}:`, error);
      session.status = 'disconnected';
      this.io.emit('session-error', {
        sessionId,
        error: error.message
      });
      throw error;
    }
  }

  async handleIncomingMessages(sessionId, messageUpdate) {
    const { messages, type } = messageUpdate;
    
    if (type !== 'notify') return;

    for (const message of messages) {
      try {
        // Skip if message is from status broadcast
        if (message.key.remoteJid === 'status@broadcast') continue;
        if (message.key.fromMe) continue; // Skip own messages

        console.log(`📨 New message in session ${sessionId} from ${message.key.remoteJid}`);

        // Emit to connected clients
        this.io.emit('message-received', {
          sessionId,
          message: this.formatMessage(message)
        });

      } catch (error) {
        console.error('Error handling incoming message:', error);
      }
    }
  }

  formatMessage(message) {
    const messageType = getContentType(message.message);
    const messageContent = message.message?.[messageType];
    
    return {
      id: message.key.id,
      from: message.key.remoteJid,
      fromMe: message.key.fromMe,
      timestamp: message.messageTimestamp,
      type: messageType,
      body: messageContent?.text || messageContent?.caption || messageContent?.conversation || '',
      hasMedia: ['imageMessage', 'videoMessage', 'audioMessage', 'documentMessage', 'stickerMessage'].includes(messageType || ''),
      participant: message.key.participant
    };
  }

  async sendMessage(sessionId, to, message) {
    const session = this.sessions.get(sessionId);
    
    if (!session || !session.socket || session.status !== 'connected') {
      throw new Error(`Session ${sessionId} is not connected`);
    }

    try {
      // Validate JID format
      if (!to.includes('@')) {
        to = to.includes('-') ? `${to}@g.us` : `${to}@s.whatsapp.net`;
      }
      
      let sentMessage;
      
      if (message.type === 'text') {
        sentMessage = await session.socket.sendMessage(to, { 
          text: message.text 
        });
      } else {
        throw new Error(`Unsupported message type: ${message.type}`);
      }

      console.log(`✅ Message sent from session ${sessionId} to ${to}`);

      return {
        success: true,
        messageId: sentMessage.key.id,
        timestamp: sentMessage.messageTimestamp
      };

    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  async getSessionStatus(sessionId) {
    return this.sessions.get(sessionId) || null;
  }

  async getAllSessions() {
    return Array.from(this.sessions.values());
  }

  async deleteSession(sessionId) {
    const session = this.sessions.get(sessionId);
    
    if (session && session.socket) {
      try {
        await session.socket.logout();
      } catch (error) {
        console.error(`Error logging out session ${sessionId}:`, error);
      }
    }
    
    this.sessions.delete(sessionId);
    
    // Delete session files
    const sessionDir = path.join(process.cwd(), 'whatsapp', 'sessions', sessionId);
    if (fs.existsSync(sessionDir)) {
      fs.rmSync(sessionDir, { recursive: true, force: true });
    }
    
    console.log(`🗑️ Session ${sessionId} deleted`);
  }

  async cleanup() {
    console.log('🧹 Cleaning up WhatsApp Gateway...');
    
    for (const [sessionId, session] of this.sessions) {
      try {
        if (session.socket) {
          await session.socket.end();
        }
      } catch (error) {
        console.error(`Error cleaning up session ${sessionId}:`, error);
      }
    }
    
    this.sessions.clear();
  }
}

module.exports = { WhatsAppGateway };
