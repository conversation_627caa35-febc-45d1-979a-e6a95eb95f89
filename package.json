{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "whatsapp:dev": "tsx watch whatsapp/server.ts", "whatsapp:build": "tsc -p whatsapp/tsconfig.json", "whatsapp:start": "node whatsapp/dist/server.js", "whatsapp:verify": "tsx whatsapp/verify-compliance.ts", "whatsapp:example": "tsx whatsapp/example.ts"}, "dependencies": {"@hapi/boom": "^10.0.1", "baileys": "^6.7.16", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "jimp": "^0.22.10", "link-preview-js": "^3.0.5", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "node-cache": "^5.1.2", "pino": "^8.17.2", "qrcode": "^1.5.3", "socket.io": "^4.7.4", "socket.io-client": "^4.8.1"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "@types/qrcode": "^1.5.5", "axios": "^1.6.4", "laravel-vite-plugin": "^1.0.0", "pino-pretty": "^13.0.0", "tsx": "^4.6.2", "typescript": "^5.3.3", "vite": "^5.0.0"}}