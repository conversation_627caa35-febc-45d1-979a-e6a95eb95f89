<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('whatsapp_devices', function (Blueprint $table) {
            $table->id();
            $table->string('device_name');
            $table->string('device_id')->unique();
            $table->enum('connection_method', ['qr_code', 'auth_code'])->default('qr_code');
            $table->enum('status', ['pending', 'connecting', 'connected', 'disconnected', 'failed'])->default('pending');
            $table->string('phone_number', 20)->nullable();
            $table->string('display_name')->nullable();
            $table->string('session_id')->nullable();
            $table->text('qr_code')->nullable();
            $table->string('auth_code', 20)->nullable();
            $table->timestamp('last_connected_at')->nullable();
            $table->timestamps();
            
            $table->index(['device_id']);
            $table->index(['status']);
            $table->index(['session_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whatsapp_devices');
    }
};
