<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add New Device - WhatsApp Gateway</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen">
        <!-- Navigation -->
        <nav class="bg-green-600 text-white p-4">
            <div class="container mx-auto flex justify-between items-center">
                <h1 class="text-2xl font-bold">
                    <i class="fab fa-whatsapp mr-2"></i>
                    WhatsApp Gateway - Add Device
                </h1>
                <div class="space-x-4">
                    <a href="<?php echo e(route('whatsapp.dashboard')); ?>" class="hover:text-green-200">Dashboard</a>
                    <a href="<?php echo e(route('whatsapp.devices.index')); ?>" class="hover:text-green-200">Device Management</a>
                    <a href="<?php echo e(route('whatsapp.sessions')); ?>" class="hover:text-green-200">Sessions</a>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="container mx-auto p-6">
            <div class="max-w-2xl mx-auto">
                <!-- Header -->
                <div class="mb-8">
                    <h2 class="text-3xl font-bold text-gray-800 mb-2">Add New WhatsApp Device</h2>
                    <p class="text-gray-600">Configure a new device to connect to WhatsApp</p>
                </div>

                <!-- Form Card -->
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <form id="deviceForm">
                        <!-- Device Name -->
                        <div class="mb-6">
                            <label for="device_name" class="block text-sm font-medium text-gray-700 mb-2">
                                Device Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" 
                                   id="device_name" 
                                   name="device_name" 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent" 
                                   placeholder="e.g., My WhatsApp Business, Customer Support, etc."
                                   required>
                            <p class="text-sm text-gray-500 mt-1">Give your device a descriptive name for easy identification</p>
                        </div>

                        <!-- Connection Method -->
                        <div class="mb-8">
                            <label class="block text-sm font-medium text-gray-700 mb-4">
                                Connection Method <span class="text-red-500">*</span>
                            </label>
                            
                            <div class="space-y-4">
                                <!-- QR Code Option -->
                                <div class="border border-gray-200 rounded-lg p-4 hover:border-green-300 transition duration-200">
                                    <label class="flex items-start cursor-pointer">
                                        <input type="radio" 
                                               name="connection_method" 
                                               value="qr_code" 
                                               class="mt-1 text-green-600 focus:ring-green-500" 
                                               checked>
                                        <div class="ml-3">
                                            <div class="flex items-center">
                                                <i class="fas fa-qrcode text-green-600 mr-2"></i>
                                                <span class="font-medium text-gray-900">QR Code Scanning</span>
                                                <span class="ml-2 px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">Recommended</span>
                                            </div>
                                            <p class="text-sm text-gray-600 mt-1">
                                                Scan a QR code with your WhatsApp mobile app to connect. 
                                                This is the fastest and most reliable method.
                                            </p>
                                            <div class="mt-2 text-xs text-gray-500">
                                                <i class="fas fa-check text-green-500 mr-1"></i> Easy setup
                                                <i class="fas fa-check text-green-500 mr-1 ml-3"></i> Secure connection
                                                <i class="fas fa-check text-green-500 mr-1 ml-3"></i> Auto-refresh QR code
                                            </div>
                                        </div>
                                    </label>
                                </div>

                                <!-- Auth Code Option -->
                                <div class="border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition duration-200">
                                    <label class="flex items-start cursor-pointer">
                                        <input type="radio" 
                                               name="connection_method" 
                                               value="auth_code" 
                                               class="mt-1 text-blue-600 focus:ring-blue-500">
                                        <div class="ml-3">
                                            <div class="flex items-center">
                                                <i class="fas fa-key text-blue-600 mr-2"></i>
                                                <span class="font-medium text-gray-900">Authentication Code</span>
                                                <span class="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Alternative</span>
                                            </div>
                                            <p class="text-sm text-gray-600 mt-1">
                                                Use an authentication code if QR scanning is not available. 
                                                You'll receive a code in WhatsApp to enter manually.
                                            </p>
                                            <div class="mt-2 text-xs text-gray-500">
                                                <i class="fas fa-info-circle text-blue-500 mr-1"></i> Manual entry required
                                                <i class="fas fa-info-circle text-blue-500 mr-1 ml-3"></i> Backup method
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Information Box -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                            <div class="flex">
                                <i class="fas fa-info-circle text-blue-600 mt-1 mr-3"></i>
                                <div>
                                    <h4 class="font-medium text-blue-900 mb-1">What happens next?</h4>
                                    <ul class="text-sm text-blue-800 space-y-1">
                                        <li>1. Your device will be created with a "Pending" status</li>
                                        <li>2. You'll be redirected to the Device Connection page</li>
                                        <li>3. Follow the on-screen instructions to connect your WhatsApp</li>
                                        <li>4. Once connected, you can start sending and receiving messages</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="flex justify-between items-center">
                            <a href="<?php echo e(route('whatsapp.devices.index')); ?>" 
                               class="px-6 py-3 text-gray-600 hover:text-gray-800 transition duration-200">
                                <i class="fas fa-arrow-left mr-2"></i>Back to Devices
                            </a>
                            
                            <button type="submit" 
                                    id="submitBtn"
                                    class="bg-green-600 text-white px-8 py-3 rounded-lg hover:bg-green-700 transition duration-200 flex items-center">
                                <i class="fas fa-plus mr-2"></i>
                                <span>Create Device</span>
                                <i class="fas fa-spinner fa-spin ml-2 hidden" id="loadingIcon"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Setup CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        // Form submission
        document.getElementById('deviceForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const loadingIcon = document.getElementById('loadingIcon');
            const formData = new FormData(e.target);
            
            // Show loading state
            submitBtn.disabled = true;
            loadingIcon.classList.remove('hidden');
            
            try {
                const response = await fetch('/api/whatsapp/devices', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': csrfToken,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        device_name: formData.get('device_name'),
                        connection_method: formData.get('connection_method')
                    })
                });

                const result = await response.json();

                if (result.success) {
                    // Show success message
                    showNotification('Device created successfully! Redirecting to connection page...', 'success');
                    
                    // Redirect to connection page after a short delay
                    setTimeout(() => {
                        window.location.href = result.redirect_url;
                    }, 1500);
                } else {
                    throw new Error(result.message || 'Failed to create device');
                }
            } catch (error) {
                showNotification('Error: ' + error.message, 'error');
                
                // Reset button state
                submitBtn.disabled = false;
                loadingIcon.classList.add('hidden');
            }
        });

        // Notification function
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
                type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
            }`;
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'} mr-2"></i>
                    <span>${message}</span>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            // Remove notification after 5 seconds
            setTimeout(() => {
                notification.remove();
            }, 5000);
        }
    </script>
</body>
</html>
<?php /**PATH C:\laragon\www\larawamd\resources\views/whatsapp/devices/create.blade.php ENDPATH**/ ?>