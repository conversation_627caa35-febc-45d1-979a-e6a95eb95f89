# WhatsApp Multi-Device Gateway

A comprehensive Laravel application with WhatsApp multi-device gateway using the latest Baileys TypeScript library (v6.7.16+).

## 🚀 **Latest Baileys Compliance**

This implementation is fully compliant with the latest Baileys library standards and includes:

- ✅ **Latest Baileys v6.7.16+** - Using the official `baileys` package
- ✅ **Optimized Performance** - Cacheable signal key store and group metadata caching
- ✅ **Enhanced Logging** - Pino logger integration for better debugging
- ✅ **Memory Efficiency** - In-memory store for message handling
- ✅ **Auto-reconnection** - Robust connection handling with proper error management
- ✅ **Multi-device Support** - Full compliance with WhatsApp's multi-device API
- ✅ **Media Handling** - Complete support for all media types with proper thumbnails
- ✅ **Group Management** - Advanced group features with metadata caching

## Features

- **Multi-Device Support**: Connect multiple WhatsApp devices simultaneously
- **Real-time Communication**: Socket.IO integration for real-time updates
- **Message Management**: Send/receive text, images, videos, audio, and documents
- **Session Management**: Create, manage, and monitor WhatsApp sessions
- **QR Code Authentication**: Easy device pairing with QR codes
- **Database Integration**: Store messages, contacts, and session data
- **Web Interface**: User-friendly dashboard for managing sessions
- **RESTful API**: Complete API for integration with other applications
- **Media Handling**: Upload and send media files
- **Contact Management**: Automatic contact extraction and storage

## Requirements

- PHP 8.1+
- Node.js 18+
- MySQL 5.7+
- Composer
- NPM/Yarn

## Installation

### 1. Install Dependencies

```bash
# Install PHP dependencies
composer install

# Install Node.js dependencies
npm install
```

### 2. Environment Configuration

The `.env` file has been configured with WhatsApp gateway settings:

```env
# WhatsApp Gateway Configuration
WHATSAPP_GATEWAY_URL=http://localhost:3000
WHATSAPP_GATEWAY_PORT=3000
WHATSAPP_SOCKET_URL=http://localhost:3000
```

### 3. Database Setup

```bash
# Run Laravel migrations
php artisan migrate
```

The WhatsApp gateway will automatically create its required tables:
- `whatsapp_sessions` - Store session information
- `whatsapp_messages` - Store message history
- `whatsapp_contacts` - Store contact information
- `whatsapp_webhooks` - Store webhook configurations

### 4. Start the Services

#### Start Laravel Application
```bash
php artisan serve
```

#### Start WhatsApp Gateway (TypeScript Server)
```bash
# Development mode with auto-reload
npm run whatsapp:dev

# Or build and run production
npm run whatsapp:build
npm run whatsapp:start
```

## Usage

### Web Interface

1. Open your browser and navigate to `http://localhost:8000`
2. You'll be redirected to the WhatsApp Gateway Dashboard
3. Click "Create New Session" to add a WhatsApp device
4. Scan the QR code with your WhatsApp mobile app
5. Once connected, you can send messages and manage the session

### Latest Baileys Features Supported

#### Advanced Message Types
- **Text Messages** - With mentions, formatting, and link previews
- **Media Messages** - Images, videos, audio, documents with thumbnails
- **Interactive Messages** - Polls, reactions, location sharing
- **View Once Messages** - Disappearing media messages
- **Stickers** - Send and receive sticker messages
- **Voice Messages** - PTT (Push-to-Talk) audio messages
- **GIF Messages** - Animated GIF support via video with gifPlayback flag

#### Enhanced Features
- **Group Metadata Caching** - Improved performance for group operations
- **Signal Key Store Caching** - Faster message encryption/decryption
- **Auto Version Detection** - Automatically uses latest WhatsApp Web version
- **Proper Browser Emulation** - Ubuntu browser profile for better compatibility
- **Message History Sync** - Selective history synchronization
- **Presence Management** - Online/offline status and typing indicators

### API Endpoints

#### Session Management

```bash
# Create a new session
POST /api/whatsapp/sessions
{
    "session_id": "device1"
}

# Get all sessions
GET /api/whatsapp/sessions

# Get specific session
GET /api/whatsapp/sessions/{sessionId}

# Delete session
DELETE /api/whatsapp/sessions/{sessionId}
```

#### Message Sending

```bash
# Send text message
POST /api/whatsapp/sessions/{sessionId}/send-text
{
    "to": "<EMAIL>",
    "text": "Hello World!"
}

# Send image
POST /api/whatsapp/sessions/{sessionId}/send-image
Content-Type: multipart/form-data
- image: [file]
- to: "<EMAIL>"
- caption: "Image caption"

# Send media (generic)
POST /api/whatsapp/sessions/{sessionId}/send-media
Content-Type: multipart/form-data
- media: [file]
- to: "<EMAIL>"
- type: "image|video|audio|document"
- caption: "Media caption"
```

#### Data Retrieval

```bash
# Get messages
GET /api/whatsapp/sessions/{sessionId}/messages?limit=50&offset=0

# Get contacts
GET /api/whatsapp/sessions/{sessionId}/contacts
```

### Socket.IO Events

Connect to `http://localhost:3000` to receive real-time events:

```javascript
const socket = io('http://localhost:3000');

// Session status updates
socket.on('session-status', (data) => {
    console.log('Session status:', data.sessionId, data.status);
});

// QR code updates
socket.on('qr-updated', (data) => {
    console.log('QR code updated:', data.sessionId);
    // Display QR code: data.qr
});

// Incoming messages
socket.on('message-received', (data) => {
    console.log('New message:', data.message);
});
```

## File Structure

```
whatsapp/
├── server.ts                 # Main server entry point
├── tsconfig.json            # TypeScript configuration
├── gateway/
│   ├── WhatsAppGateway.ts   # Main gateway class
│   ├── MessageHandler.ts    # Message processing
│   └── MediaHandler.ts      # Media file handling
├── database/
│   └── DatabaseManager.ts   # Database operations
├── routes/
│   └── api.ts              # API route definitions
├── sessions/               # Session storage (auto-created)
├── media/                  # Media files (auto-created)
└── uploads/               # Temporary uploads (auto-created)
```

## Configuration

### WhatsApp Gateway Settings

Edit `config/whatsapp.php` to customize:

- Gateway URL and port
- Session timeout and retry settings
- Media file size limits and allowed types
- Webhook configuration
- Database storage options
- Rate limiting
- Logging preferences

### Environment Variables

Key environment variables in `.env`:

```env
WHATSAPP_GATEWAY_URL=http://localhost:3000
WHATSAPP_GATEWAY_PORT=3000
WHATSAPP_MAX_FILE_SIZE=52428800
WHATSAPP_STORE_MESSAGES=true
WHATSAPP_RATE_LIMIT_ENABLED=true
WHATSAPP_MESSAGES_PER_MINUTE=20
```

## Development

### TypeScript Development

```bash
# Watch mode for development
npm run whatsapp:dev

# Build TypeScript
npm run whatsapp:build

# Run built JavaScript
npm run whatsapp:start
```

### Laravel Development

```bash
# Start Laravel development server
php artisan serve

# Run database migrations
php artisan migrate

# Clear cache
php artisan cache:clear
php artisan config:clear
```

## Troubleshooting

### Common Issues

1. **Port 3000 already in use**
   - Change `WHATSAPP_GATEWAY_PORT` in `.env`
   - Update the port in `whatsapp/server.ts`

2. **Database connection errors**
   - Verify MySQL is running
   - Check database credentials in `.env`
   - Run `php artisan migrate`

3. **QR code not displaying**
   - Ensure WhatsApp gateway server is running
   - Check browser console for Socket.IO connection errors
   - Verify `WHATSAPP_SOCKET_URL` in `.env`

4. **Messages not sending**
   - Confirm session is connected (status: 'connected')
   - Check phone number format (include country code)
   - Verify recipient exists on WhatsApp

### Logs

- Laravel logs: `storage/logs/laravel.log`
- WhatsApp gateway logs: Console output when running `npm run whatsapp:dev`

## Security Considerations

1. **Authentication**: Add authentication middleware to protect API endpoints
2. **Rate Limiting**: Configure appropriate rate limits to prevent abuse
3. **File Uploads**: Validate file types and sizes for media uploads
4. **Database**: Use proper database credentials and restrict access
5. **CORS**: Configure CORS settings for production use

## Production Deployment

1. **Environment**: Set `APP_ENV=production` in `.env`
2. **Database**: Use production database credentials
3. **Process Manager**: Use PM2 or similar for the Node.js server
4. **Web Server**: Configure Nginx/Apache for Laravel
5. **SSL**: Enable HTTPS for secure communication
6. **Monitoring**: Set up logging and monitoring for both services

## License

This project is open-source and available under the MIT License.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the logs for error messages
3. Ensure all dependencies are properly installed
4. Verify environment configuration

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request