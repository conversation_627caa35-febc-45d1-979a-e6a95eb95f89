<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('whatsapp_sessions', function (Blueprint $table) {
            // Add device_id column if it doesn't exist
            if (!Schema::hasColumn('whatsapp_sessions', 'device_id')) {
                $table->unsignedBigInteger('device_id')->nullable()->after('session_id');
                $table->index(['device_id']);
                
                // Add foreign key constraint
                $table->foreign('device_id')->references('id')->on('whatsapp_devices')->onDelete('set null');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('whatsapp_sessions', function (Blueprint $table) {
            if (Schema::hasColumn('whatsapp_sessions', 'device_id')) {
                $table->dropForeign(['device_id']);
                $table->dropIndex(['device_id']);
                $table->dropColumn('device_id');
            }
        });
    }
};
