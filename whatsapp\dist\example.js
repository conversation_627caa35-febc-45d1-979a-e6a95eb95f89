import makeWASocket, { DisconnectReason, useMultiFileAuthState, makeCacheableSignalKeyStore, Browsers, makeInMemoryStore, fetchLatestBaileysVersion, getContentType, downloadMediaMessage } from 'baileys';
import pino from 'pino';
import NodeCache from 'node-cache';
// Initialize logger
const logger = pino({
    level: 'info',
    transport: {
        target: 'pino-pretty',
        options: {
            colorize: true
        }
    }
});
// Initialize in-memory store
const store = makeInMemoryStore({ logger });
// Initialize group cache
const groupCache = new NodeCache({ stdTTL: 5 * 60, useClones: false });
async function connectToWhatsApp() {
    const { state, saveCreds } = await useMultiFileAuthState('auth_info_baileys');
    // Fetch latest Baileys version
    const { version, isLatest } = await fetchLatestBaileysVersion();
    logger.info(`Using Baileys version: ${version}, isLatest: ${isLatest}`);
    const sock = makeWASocket({
        version,
        auth: {
            creds: state.creds,
            keys: makeCacheableSignalKeyStore(state.keys, logger)
        },
        printQRInTerminal: true,
        logger,
        browser: Browsers.ubuntu('WhatsApp Example'),
        generateHighQualityLinkPreview: true,
        syncFullHistory: false,
        markOnlineOnConnect: true,
        getMessage: async (key) => {
            if (store) {
                const msg = await store.loadMessage(key.remoteJid, key.id);
                return msg?.message || undefined;
            }
            return undefined;
        },
        cachedGroupMetadata: async (jid) => groupCache.get(jid),
        shouldSyncHistoryMessage: msg => {
            return !!msg.message?.conversation || !!msg.message?.extendedTextMessage?.text;
        }
    });
    // Bind store to socket
    store.bind(sock.ev);
    // Handle connection updates
    sock.ev.on('connection.update', (update) => {
        const { connection, lastDisconnect } = update;
        if (connection === 'close') {
            const shouldReconnect = lastDisconnect?.error?.output?.statusCode !== DisconnectReason.loggedOut;
            logger.info('Connection closed due to', lastDisconnect?.error, ', reconnecting', shouldReconnect);
            if (shouldReconnect) {
                connectToWhatsApp();
            }
        }
        else if (connection === 'open') {
            logger.info('Connection opened successfully');
        }
    });
    // Handle incoming messages
    sock.ev.on('messages.upsert', async (messageUpdate) => {
        const { messages, type } = messageUpdate;
        if (type !== 'notify')
            return;
        for (const message of messages) {
            if (!message.message)
                continue;
            if (message.key.fromMe)
                continue; // Skip own messages
            if (message.key.remoteJid === 'status@broadcast')
                continue; // Skip status updates
            const messageType = getContentType(message.message);
            const from = message.key.remoteJid;
            const text = message.message.conversation ||
                message.message.extendedTextMessage?.text ||
                message.message[messageType]?.caption || '';
            logger.info(`Received ${messageType} from ${from}: ${text}`);
            // Example: Echo text messages
            if (messageType === 'conversation' || messageType === 'extendedTextMessage') {
                await sock.sendMessage(from, { text: `Echo: ${text}` });
            }
            // Example: Handle media messages
            if (['imageMessage', 'videoMessage', 'audioMessage', 'documentMessage'].includes(messageType)) {
                try {
                    const buffer = await downloadMediaMessage(message, 'buffer', {}, {
                        logger,
                        reuploadRequest: sock.updateMediaMessage
                    });
                    if (buffer) {
                        logger.info(`Downloaded ${messageType} of size: ${buffer.length} bytes`);
                        // You can save the buffer to file or process it further
                    }
                }
                catch (error) {
                    logger.error('Error downloading media:', error);
                }
            }
        }
    });
    // Handle group metadata caching
    sock.ev.on('groups.update', async (updates) => {
        for (const update of updates) {
            try {
                const metadata = await sock.groupMetadata(update.id);
                groupCache.set(update.id, metadata);
                logger.info(`Updated group metadata for: ${metadata.subject}`);
            }
            catch (error) {
                logger.error('Error updating group metadata:', error);
            }
        }
    });
    sock.ev.on('group-participants.update', async (update) => {
        try {
            const metadata = await sock.groupMetadata(update.id);
            groupCache.set(update.id, metadata);
            logger.info(`Updated group participants for: ${metadata.subject}`);
        }
        catch (error) {
            logger.error('Error updating group participants:', error);
        }
    });
    // Handle credential updates
    sock.ev.on('creds.update', saveCreds);
    // Handle presence updates
    sock.ev.on('presence.update', (presenceUpdate) => {
        logger.info('Presence update:', presenceUpdate);
    });
    // Example functions for different message types
    const examples = {
        // Send text message
        sendText: async (jid, text) => {
            return await sock.sendMessage(jid, { text });
        },
        // Send image with caption
        sendImage: async (jid, imageUrl, caption) => {
            return await sock.sendMessage(jid, {
                image: { url: imageUrl },
                caption
            });
        },
        // Send video
        sendVideo: async (jid, videoUrl, caption) => {
            return await sock.sendMessage(jid, {
                video: { url: videoUrl },
                caption
            });
        },
        // Send audio
        sendAudio: async (jid, audioUrl) => {
            return await sock.sendMessage(jid, {
                audio: { url: audioUrl },
                mimetype: 'audio/mp4'
            });
        },
        // Send document
        sendDocument: async (jid, documentUrl, fileName) => {
            return await sock.sendMessage(jid, {
                document: { url: documentUrl },
                fileName,
                mimetype: 'application/pdf'
            });
        },
        // Send location
        sendLocation: async (jid, latitude, longitude) => {
            return await sock.sendMessage(jid, {
                location: {
                    degreesLatitude: latitude,
                    degreesLongitude: longitude
                }
            });
        },
        // Send poll
        sendPoll: async (jid, name, values) => {
            return await sock.sendMessage(jid, {
                poll: {
                    name,
                    values,
                    selectableCount: 1
                }
            });
        },
        // Send reaction
        sendReaction: async (jid, messageKey, emoji) => {
            return await sock.sendMessage(jid, {
                react: {
                    text: emoji,
                    key: messageKey
                }
            });
        },
        // Check if number exists on WhatsApp
        checkExists: async (number) => {
            const [result] = await sock.onWhatsApp(number);
            return result?.exists;
        },
        // Get profile picture
        getProfilePicture: async (jid) => {
            try {
                return await sock.profilePictureUrl(jid, 'image');
            }
            catch (error) {
                return null;
            }
        },
        // Update presence
        updatePresence: async (jid, presence) => {
            return await sock.sendPresenceUpdate(presence, jid);
        }
    };
    return { sock, examples };
}
// Start the connection
connectToWhatsApp()
    .then(({ sock, examples }) => {
    logger.info('WhatsApp connection established');
    // Example usage:
    // examples.sendText('<EMAIL>', 'Hello from Baileys!');
    // examples.checkExists('<EMAIL>').then(exists => console.log('Exists:', exists));
})
    .catch(error => {
    logger.error('Failed to connect:', error);
});
export default connectToWhatsApp;
//# sourceMappingURL=example.js.map