{"version": 3, "file": "WhatsAppGateway.cjs", "sourceRoot": "", "sources": ["../../gateway/WhatsAppGateway.cjs"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,MAAM,EAAE,YAAY,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,QAAQ,EAAE,yBAAyB,EAAE,2BAA2B,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;AACvK,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;AACjC,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;AAC7B,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AACzB,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;AAC7B,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;AAExC,MAAM,eAAe;IACnB,YAAY,EAAE,EAAE,SAAS;QACvB,IAAI,CAAC,QAAQ,GAAG,IAAI,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAE3B,oBAAoB;QACpB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACjB,KAAK,EAAE,MAAM;YACb,SAAS,EAAE;gBACT,MAAM,EAAE,aAAa;gBACrB,OAAO,EAAE;oBACP,QAAQ,EAAE,IAAI;iBACf;aACF;SACF,CAAC,CAAC;QAEH,oDAAoD;QACpD,IAAI,CAAC,KAAK,GAAG,IAAI,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;QACjE,IAAI,CAAC,UAAU,GAAG,IAAI,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;QAEtE,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;IAC9D,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,SAAS;QAC3B,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YACjC,MAAM,IAAI,KAAK,CAAC,WAAW,SAAS,iBAAiB,CAAC,CAAC;QACzD,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;QAE/E,kCAAkC;QAClC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;YAC/B,EAAE,CAAC,SAAS,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAChD,CAAC;QAED,MAAM,OAAO,GAAG;YACd,SAAS;YACT,MAAM,EAAE,IAAI;YACZ,EAAE,EAAE,IAAI;YACR,MAAM,EAAE,YAAY;YACpB,IAAI,EAAE,IAAI;SACX,CAAC;QAEF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAEtC,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,wBAAwB,SAAS,EAAE,CAAC,CAAC;YAEjD,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,MAAM,qBAAqB,CAAC,UAAU,CAAC,CAAC;YAErE,wDAAwD;YACxD,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,MAAM,yBAAyB,EAAE,CAAC;YAChE,OAAO,CAAC,GAAG,CAAC,4BAA4B,OAAO,eAAe,QAAQ,EAAE,CAAC,CAAC;YAE1E,MAAM,MAAM,GAAG,YAAY,CAAC;gBAC1B,OAAO;gBACP,IAAI,EAAE;oBACJ,KAAK,EAAE,KAAK,CAAC,KAAK;oBAClB,IAAI,EAAE,2BAA2B,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;iBAC3D;gBACD,iBAAiB,EAAE,IAAI,EAAE,uCAAuC;gBAChE,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC,kBAAkB,CAAC;gBAC5C,8BAA8B,EAAE,IAAI;gBACpC,eAAe,EAAE,KAAK;gBACtB,mBAAmB,EAAE,IAAI;gBACzB,UAAU,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE;oBACxB,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;wBACf,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,SAAS,IAAI,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;wBACzD,OAAO,GAAG,EAAE,OAAO,IAAI,SAAS,CAAC;oBACnC,CAAC;oBACD,OAAO,SAAS,CAAC;gBACnB,CAAC;aACF,CAAC,CAAC;YAEH,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;YACxB,OAAO,CAAC,GAAG,CAAC,iCAAiC,SAAS,EAAE,CAAC,CAAC;YAE1D,4BAA4B;YAC5B,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,mBAAmB,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE;gBACjD,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,EAAE,EAAE,GAAG,MAAM,CAAC;gBAClD,OAAO,CAAC,GAAG,CAAC,4BAA4B,SAAS,GAAG,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;gBAEhF,IAAI,EAAE,EAAE,CAAC;oBACP,IAAI,CAAC;wBACH,OAAO,CAAC,GAAG,CAAC,sCAAsC,SAAS,EAAE,CAAC,CAAC;wBAC/D,OAAO,CAAC,EAAE,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;wBACxC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;wBAEtB,sBAAsB;wBACtB,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,YAAY,EAAE;4BACzB,SAAS;4BACT,EAAE,EAAE,OAAO,CAAC,EAAE;yBACf,CAAC,CAAC;wBAEH,kBAAkB;wBAClB,MAAM,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,SAAS,EAAE;4BAC5C,MAAM,EAAE,IAAI;4BACZ,OAAO,EAAE,OAAO,CAAC,EAAE;yBACpB,CAAC,CAAC;wBAEH,OAAO,CAAC,GAAG,CAAC,gDAAgD,SAAS,EAAE,CAAC,CAAC;oBAC3E,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;wBACrE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,eAAe,EAAE;4BAC5B,SAAS;4BACT,KAAK,EAAE,4BAA4B;yBACpC,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;gBAED,IAAI,UAAU,KAAK,OAAO,EAAE,CAAC;oBAC3B,MAAM,eAAe,GAAG,cAAc,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,KAAK,gBAAgB,CAAC,SAAS,CAAC;oBAEjG,IAAI,eAAe,EAAE,CAAC;wBACpB,OAAO,CAAC,GAAG,CAAC,cAAc,SAAS,gCAAgC,CAAC,CAAC;wBACrE,OAAO,CAAC,MAAM,GAAG,YAAY,CAAC;wBAC9B,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC,CAAC;wBAEpE,+BAA+B;wBAC/B,UAAU,CAAC,GAAG,EAAE;4BACd,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBACrD,CAAC,EAAE,IAAI,CAAC,CAAC;oBACX,CAAC;yBAAM,CAAC;wBACN,OAAO,CAAC,GAAG,CAAC,aAAa,SAAS,aAAa,CAAC,CAAC;wBACjD,OAAO,CAAC,MAAM,GAAG,cAAc,CAAC;wBAChC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;wBAChC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,cAAc,EAAE,CAAC,CAAC;wBAEtE,MAAM,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,SAAS,EAAE;4BAC5C,MAAM,EAAE,cAAc;yBACvB,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;qBAAM,IAAI,UAAU,KAAK,MAAM,EAAE,CAAC;oBACjC,OAAO,CAAC,GAAG,CAAC,cAAc,SAAS,yBAAyB,CAAC,CAAC;oBAC9D,OAAO,CAAC,MAAM,GAAG,WAAW,CAAC;oBAC7B,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;oBAC3B,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC;oBAElB,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,EAAE;wBAC7B,SAAS;wBACT,MAAM,EAAE,WAAW;wBACnB,IAAI,EAAE,OAAO,CAAC,IAAI;qBACnB,CAAC,CAAC;oBAEH,MAAM,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,SAAS,EAAE;wBAC5C,MAAM,EAAE,WAAW;wBACnB,YAAY,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;wBACrD,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,IAAI,IAAI;wBAChC,OAAO,EAAE,IAAI;qBACd,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,4BAA4B;YAC5B,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;YAExC,2BAA2B;YAC3B,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,iBAAiB,EAAE,KAAK,EAAE,aAAa,EAAE,EAAE;gBACtD,MAAM,IAAI,CAAC,sBAAsB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;YAC9D,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,aAAa,SAAS,yCAAyC,CAAC,CAAC;QAE/E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;YAC/D,OAAO,CAAC,MAAM,GAAG,cAAc,CAAC;YAChC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,eAAe,EAAE;gBAC5B,SAAS;gBACT,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,SAAS,EAAE,aAAa;QACnD,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,aAAa,CAAC;QAEzC,IAAI,IAAI,KAAK,QAAQ;YAAE,OAAO;QAE9B,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,IAAI,CAAC;gBACH,2CAA2C;gBAC3C,IAAI,OAAO,CAAC,GAAG,CAAC,SAAS,KAAK,kBAAkB;oBAAE,SAAS;gBAC3D,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM;oBAAE,SAAS,CAAC,oBAAoB;gBAEtD,OAAO,CAAC,GAAG,CAAC,6BAA6B,SAAS,SAAS,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC;gBAEpF,4BAA4B;gBAC5B,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE;oBAC/B,SAAS;oBACT,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;iBACrC,CAAC,CAAC;YAEL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC;IACH,CAAC;IAED,aAAa,CAAC,OAAO;QACnB,MAAM,WAAW,GAAG,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACpD,MAAM,cAAc,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC,WAAW,CAAC,CAAC;QAEtD,OAAO;YACL,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE;YAClB,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS;YAC3B,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,MAAM;YAC1B,SAAS,EAAE,OAAO,CAAC,gBAAgB;YACnC,IAAI,EAAE,WAAW;YACjB,IAAI,EAAE,cAAc,EAAE,IAAI,IAAI,cAAc,EAAE,OAAO,IAAI,cAAc,EAAE,YAAY,IAAI,EAAE;YAC3F,QAAQ,EAAE,CAAC,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,iBAAiB,EAAE,gBAAgB,CAAC,CAAC,QAAQ,CAAC,WAAW,IAAI,EAAE,CAAC;YAC3H,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW;SACrC,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,SAAS,EAAE,EAAE,EAAE,OAAO;QACtC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAE7C,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;YAClE,MAAM,IAAI,KAAK,CAAC,WAAW,SAAS,mBAAmB,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC;YACH,sBAAsB;YACtB,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBACtB,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,iBAAiB,CAAC;YAChE,CAAC;YAED,IAAI,WAAW,CAAC;YAEhB,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;gBAC5B,WAAW,GAAG,MAAM,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE,EAAE;oBACjD,IAAI,EAAE,OAAO,CAAC,IAAI;iBACnB,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,6BAA6B,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;YAC/D,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,+BAA+B,SAAS,OAAO,EAAE,EAAE,CAAC,CAAC;YAEjE,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,WAAW,CAAC,GAAG,CAAC,EAAE;gBAC7B,SAAS,EAAE,WAAW,CAAC,gBAAgB;aACxC,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC/C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,SAAS;QAC9B,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC;IAC9C,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,SAAS;QAC3B,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAE7C,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YAC9B,IAAI,CAAC;gBACH,MAAM,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YAChC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;YAClE,CAAC;QACH,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAEhC,uBAAuB;QACvB,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;QAC/E,IAAI,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;YAC9B,EAAE,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;QAC1D,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,eAAe,SAAS,UAAU,CAAC,CAAC;IAClD,CAAC;IAED,KAAK,CAAC,OAAO;QACX,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAElD,KAAK,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjD,IAAI,CAAC;gBACH,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;oBACnB,MAAM,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;gBAC7B,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;YAClE,CAAC;QACH,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;IACxB,CAAC;CACF;AAED,MAAM,CAAC,OAAO,GAAG,EAAE,eAAe,EAAE,CAAC"}