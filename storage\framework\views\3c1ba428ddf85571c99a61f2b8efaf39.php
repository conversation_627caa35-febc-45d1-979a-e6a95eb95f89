<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session Details - <?php echo e($sessionId); ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .qr-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-connecting { background-color: #f59e0b; animation: pulse 1s infinite; }
        .status-qr { background-color: #3b82f6; animation: pulse 1s infinite; }
        .status-connected { background-color: #10b981; }
        .status-disconnected { background-color: #ef4444; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="container mx-auto px-6 py-4">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-4">
                    <a href="<?php echo e(route('whatsapp.sessions')); ?>" class="text-gray-600 hover:text-gray-800">
                        <i class="fas fa-arrow-left"></i> Back to Sessions
                    </a>
                    <div class="h-6 w-px bg-gray-300"></div>
                    <h1 class="text-xl font-semibold text-gray-800">
                        <i class="fab fa-whatsapp text-green-500 mr-2"></i>
                        Session: <?php echo e($sessionId); ?>

                    </h1>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center">
                        <span class="status-indicator" id="status-indicator"></span>
                        <span class="text-sm text-gray-600" id="status-text">Loading...</span>
                    </div>
                    <button onclick="refreshSession()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                        <i class="fas fa-sync mr-2"></i>Refresh
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mx-auto px-6 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Session Information -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Status Card -->
                <div class="bg-white rounded-xl shadow-sm border">
                    <div class="p-6">
                        <h2 class="text-lg font-semibold text-gray-800 mb-4">Session Status</h2>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm text-gray-600">Session ID</p>
                                <p class="font-mono bg-gray-100 px-2 py-1 rounded text-sm"><?php echo e($sessionId); ?></p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Current Status</p>
                                <p class="font-semibold" id="current-status"><?php echo e($session['status'] ?? 'Unknown'); ?></p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Phone Number</p>
                                <p class="font-semibold" id="phone-number"><?php echo e($session['info']['id'] ?? 'Not connected'); ?></p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Display Name</p>
                                <p class="font-semibold" id="display-name"><?php echo e($session['info']['name'] ?? 'Not connected'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions Card -->
                <div class="bg-white rounded-xl shadow-sm border">
                    <div class="p-6">
                        <h2 class="text-lg font-semibold text-gray-800 mb-4">Actions</h2>
                        <div class="grid grid-cols-2 gap-4">
                            <button onclick="connectDevice()" class="bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700" id="connect-btn">
                                <i class="fas fa-qrcode mr-2"></i>Connect Device
                            </button>
                            <button onclick="sendMessage()" class="bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700" id="send-btn" disabled>
                                <i class="fas fa-paper-plane mr-2"></i>Send Message
                            </button>
                            <button onclick="viewMessages()" class="bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700" id="messages-btn" disabled>
                                <i class="fas fa-comments mr-2"></i>View Messages
                            </button>
                            <button onclick="deleteSession()" class="bg-red-600 text-white py-3 px-4 rounded-lg hover:bg-red-700">
                                <i class="fas fa-trash mr-2"></i>Delete Session
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Recent Messages -->
                <div class="bg-white rounded-xl shadow-sm border">
                    <div class="p-6">
                        <h2 class="text-lg font-semibold text-gray-800 mb-4">Recent Messages</h2>
                        <div id="messages-container" class="space-y-3 max-h-96 overflow-y-auto">
                            <div class="text-center text-gray-500 py-8">
                                <i class="fas fa-comments text-4xl mb-4"></i>
                                <p>No messages yet</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- QR Code Section -->
            <div class="space-y-6">
                <!-- QR Code Card -->
                <div class="bg-white rounded-xl shadow-sm border">
                    <div class="p-6">
                        <h2 class="text-lg font-semibold text-gray-800 mb-4">
                            <i class="fas fa-qrcode text-blue-500 mr-2"></i>
                            QR Code
                        </h2>
                        
                        <div id="qr-section">
                            <?php if(isset($session['qr']) && $session['qr']): ?>
                                <div class="text-center">
                                    <div class="qr-container p-4 rounded-xl mb-4">
                                        <img src="<?php echo e($session['qr']); ?>" alt="WhatsApp QR Code" class="w-full max-w-xs mx-auto bg-white p-2 rounded-lg">
                                    </div>
                                    <p class="text-sm text-gray-600 mb-4">Scan with WhatsApp mobile app</p>
                                    <button onclick="refreshQR()" class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700">
                                        <i class="fas fa-sync mr-2"></i>Refresh QR
                                    </button>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-8" id="no-qr">
                                    <i class="fas fa-mobile-alt text-4xl text-gray-400 mb-4"></i>
                                    <p class="text-gray-600 mb-4">QR code not available</p>
                                    <?php if(($session['status'] ?? '') === 'disconnected'): ?>
                                        <button onclick="generateQR()" class="bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700">
                                            <i class="fas fa-qrcode mr-2"></i>Generate QR Code
                                        </button>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Connection Guide -->
                <div class="bg-white rounded-xl shadow-sm border">
                    <div class="p-6">
                        <h2 class="text-lg font-semibold text-gray-800 mb-4">
                            <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                            How to Connect
                        </h2>
                        
                        <div class="space-y-3 text-sm">
                            <div class="flex items-start space-x-2">
                                <span class="w-5 h-5 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-xs font-medium flex-shrink-0 mt-0.5">1</span>
                                <p>Open WhatsApp on your phone</p>
                            </div>
                            <div class="flex items-start space-x-2">
                                <span class="w-5 h-5 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-xs font-medium flex-shrink-0 mt-0.5">2</span>
                                <p>Go to Settings → Linked Devices</p>
                            </div>
                            <div class="flex items-start space-x-2">
                                <span class="w-5 h-5 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-xs font-medium flex-shrink-0 mt-0.5">3</span>
                                <p>Tap "Link a Device"</p>
                            </div>
                            <div class="flex items-start space-x-2">
                                <span class="w-5 h-5 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-xs font-medium flex-shrink-0 mt-0.5">4</span>
                                <p>Scan the QR code above</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Session Stats -->
                <div class="bg-white rounded-xl shadow-sm border">
                    <div class="p-6">
                        <h2 class="text-lg font-semibold text-gray-800 mb-4">Statistics</h2>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Messages Sent</span>
                                <span class="font-semibold" id="messages-sent">0</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Messages Received</span>
                                <span class="font-semibold" id="messages-received">0</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Uptime</span>
                                <span class="font-semibold" id="uptime">-</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const sessionId = '<?php echo e($sessionId); ?>';
        const socket = io('<?php echo e(config("whatsapp.socket_url", "http://localhost:3000")); ?>');
        
        // Socket event handlers
        socket.on('connect', () => {
            console.log('Connected to WhatsApp Gateway');
        });
        
        socket.on('session-status', (data) => {
            if (data.sessionId === sessionId) {
                updateSessionStatus(data.status, data.info);
            }
        });
        
        socket.on('qr-updated', (data) => {
            if (data.sessionId === sessionId) {
                updateQRCode(data.qr);
            }
        });
        
        socket.on('message-received', (data) => {
            if (data.sessionId === sessionId) {
                addMessage(data.message, 'received');
            }
        });
        
        socket.on('message-sent', (data) => {
            if (data.sessionId === sessionId) {
                addMessage(data.message, 'sent');
            }
        });
        
        // Functions
        function updateSessionStatus(status, info) {
            const statusIndicator = document.getElementById('status-indicator');
            const statusText = document.getElementById('status-text');
            const currentStatus = document.getElementById('current-status');
            
            statusIndicator.className = 'status-indicator status-' + status;
            statusText.textContent = status.charAt(0).toUpperCase() + status.slice(1);
            currentStatus.textContent = status.charAt(0).toUpperCase() + status.slice(1);
            
            // Update info if available
            if (info) {
                document.getElementById('phone-number').textContent = info.id || 'Unknown';
                document.getElementById('display-name').textContent = info.name || 'Unknown';
            }
            
            // Update button states
            const connectBtn = document.getElementById('connect-btn');
            const sendBtn = document.getElementById('send-btn');
            const messagesBtn = document.getElementById('messages-btn');
            
            if (status === 'connected') {
                connectBtn.textContent = 'Connected';
                connectBtn.className = 'bg-green-500 text-white py-3 px-4 rounded-lg cursor-not-allowed';
                connectBtn.disabled = true;
                sendBtn.disabled = false;
                messagesBtn.disabled = false;
                sendBtn.className = 'bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700';
                messagesBtn.className = 'bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700';
            } else {
                connectBtn.disabled = false;
                sendBtn.disabled = true;
                messagesBtn.disabled = true;
                connectBtn.className = 'bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700';
                sendBtn.className = 'bg-gray-400 text-white py-3 px-4 rounded-lg cursor-not-allowed';
                messagesBtn.className = 'bg-gray-400 text-white py-3 px-4 rounded-lg cursor-not-allowed';
            }
        }
        
        function updateQRCode(qrData) {
            const qrSection = document.getElementById('qr-section');
            qrSection.innerHTML = `
                <div class="text-center">
                    <div class="qr-container p-4 rounded-xl mb-4">
                        <img src="${qrData}" alt="WhatsApp QR Code" class="w-full max-w-xs mx-auto bg-white p-2 rounded-lg">
                    </div>
                    <p class="text-sm text-gray-600 mb-4">Scan with WhatsApp mobile app</p>
                    <button onclick="refreshQR()" class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700">
                        <i class="fas fa-sync mr-2"></i>Refresh QR
                    </button>
                </div>
            `;
        }
        
        function addMessage(message, type) {
            const container = document.getElementById('messages-container');
            
            // Remove "no messages" placeholder if it exists
            const placeholder = container.querySelector('.text-center');
            if (placeholder) {
                placeholder.remove();
            }
            
            const messageEl = document.createElement('div');
            messageEl.className = `p-3 rounded-lg ${type === 'sent' ? 'bg-green-100 ml-8' : 'bg-gray-100 mr-8'}`;
            messageEl.innerHTML = `
                <div class="flex justify-between items-start mb-1">
                    <span class="text-sm font-medium">${type === 'sent' ? 'Sent' : message.from || 'Received'}</span>
                    <span class="text-xs text-gray-500">${new Date().toLocaleTimeString()}</span>
                </div>
                <p class="text-sm">${message.body || message.text || '[Media Message]'}</p>
            `;
            
            container.appendChild(messageEl);
            container.scrollTop = container.scrollHeight;
        }
        
        function connectDevice() {
            window.location.href = `/whatsapp/connect-device/${sessionId}`;
        }
        
        function sendMessage() {
            window.location.href = `/whatsapp/sessions/${sessionId}/send`;
        }
        
        function viewMessages() {
            window.location.href = `/whatsapp/sessions/${sessionId}/messages`;
        }
        
        async function deleteSession() {
            if (!confirm('Are you sure you want to delete this session?')) return;
            
            try {
                const response = await fetch(`/api/whatsapp/sessions/${sessionId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    window.location.href = '<?php echo e(route("whatsapp.sessions")); ?>';
                } else {
                    alert('Failed to delete session: ' + (result.message || 'Unknown error'));
                }
            } catch (error) {
                alert('Error deleting session: ' + error.message);
            }
        }
        
        function refreshSession() {
            location.reload();
        }
        
        function refreshQR() {
            generateQR();
        }
        
        async function generateQR() {
            try {
                const response = await fetch(`/api/whatsapp/sessions/${sessionId}/qr`, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    // QR will be updated via socket
                } else {
                    alert('Failed to generate QR code: ' + (result.message || 'Unknown error'));
                }
            } catch (error) {
                alert('Error generating QR code: ' + error.message);
            }
        }
        
        // Initialize status
        updateSessionStatus('<?php echo e($session["status"] ?? "unknown"); ?>', <?php echo json_encode($session['info'] ?? []); ?>);
    </script>
</body>
</html>
<?php /**PATH C:\laragon\www\larawamd\resources\views/whatsapp/session-detail.blade.php ENDPATH**/ ?>