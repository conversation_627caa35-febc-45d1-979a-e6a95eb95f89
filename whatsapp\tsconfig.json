{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "outDir": "./dist", "rootDir": "./", "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "lib": ["ES2022", "DOM"], "experimentalDecorators": true, "emitDecoratorMetadata": true, "allowJs": true, "noEmitOnError": false, "isolatedModules": true}, "include": ["**/*.ts"], "exclude": ["node_modules", "dist"]}