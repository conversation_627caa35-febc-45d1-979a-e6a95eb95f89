import { WASocket } from 'baileys';
import { Server } from 'socket.io';
import { DatabaseManager } from '../database/DatabaseManager';
export interface WhatsAppSession {
    sessionId: string;
    socket: WASocket | null;
    qr: string | null;
    status: 'connecting' | 'connected' | 'disconnected' | 'qr';
    info: any;
}
export declare class WhatsAppGateway {
    private sessions;
    private io;
    private dbManager;
    private messageHandler;
    private mediaHandler;
    private logger;
    private store;
    private groupCache;
    constructor(io: Server, dbManager: DatabaseManager);
    private initializeExistingSessions;
    createSession(sessionId: string): Promise<void>;
    private handleIncomingMessages;
    private formatMessage;
    sendMessage(sessionId: string, to: string, message: any): Promise<any>;
    getSessionStatus(sessionId: string): Promise<WhatsAppSession | null>;
    getAllSessions(): Promise<WhatsAppSession[]>;
    deleteSession(sessionId: string): Promise<void>;
    downloadMedia(sessionId: string, messageId: string): Promise<Buffer | null>;
    cleanup(): Promise<void>;
}
//# sourceMappingURL=WhatsAppGateway.d.ts.map